#!/usr/bin/env python3
"""
测试修复后的加载功能
"""

from multimodal_vector_db_manager import MilvusMultimodalManager

def test_load_collection():
    print("=== 测试修复后的集合加载 ===\n")
    
    # 初始化管理器
    try:
        milvus_manager = MilvusMultimodalManager(
            uri="http://localhost:19530",
            collection_name="multimodal_chinese_clip"
        )
        print("✓ Milvus管理器初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return False
    
    # 运行诊断
    print("\n1. 运行集合诊断...")
    milvus_manager.diagnose_collection()
    
    # 测试加载
    print("\n2. 测试集合加载...")
    try:
        # 先释放集合（如果已加载）
        try:
            current_state = milvus_manager.milvus_client.get_load_state(
                collection_name=milvus_manager.collection_name
            )
            if 'Loaded' in str(current_state.get('state', '')):
                print("集合已加载，先释放...")
                milvus_manager.milvus_client.release_collection(
                    collection_name=milvus_manager.collection_name
                )
                import time
                time.sleep(2)
                print("释放完成")
        except Exception as e:
            print(f"释放过程中的警告: {e}")
        
        # 现在测试加载
        load_result = milvus_manager.load_collection(timeout=30)
        
        if load_result:
            print("✓ 集合加载成功！")
            
            # 验证最终状态
            final_state = milvus_manager.milvus_client.get_load_state(
                collection_name=milvus_manager.collection_name
            )
            print(f"最终状态: {final_state}")
            return True
        else:
            print("✗ 集合加载失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_setup_collection():
    """测试完整的集合设置流程"""
    print("\n=== 测试完整集合设置流程 ===\n")
    
    try:
        milvus_manager = MilvusMultimodalManager(
            uri="http://localhost:19530",
            collection_name="multimodal_chinese_clip"
        )
        
        # 测试setup_collection方法
        print("运行 setup_collection...")
        result = milvus_manager.setup_collection(recreate=False)  # 不重新创建
        
        if result:
            print("✓ 集合设置成功！")
            
            # 检查最终状态
            stats = milvus_manager.get_collection_stats()
            print(f"集合统计信息: {stats}")
            
            return True
        else:
            print("✗ 集合设置失败")
            return False
            
    except Exception as e:
        print(f"✗ 设置过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修复后的功能...\n")
    
    # 测试1: 单独的加载功能
    load_success = test_load_collection()
    
    # 测试2: 完整的设置流程
    setup_success = test_setup_collection()
    
    print(f"\n=== 测试结果 ===")
    print(f"加载测试: {'✓ 成功' if load_success else '✗ 失败'}")
    print(f"设置测试: {'✓ 成功' if setup_success else '✗ 失败'}")
    
    if load_success and setup_success:
        print("\n🎉 所有测试通过！你的集合现在可以正常使用了。")
        print("\n注意: 你的集合目前没有数据 (row_count: 0)")
        print("如果需要插入数据，请使用 ImageEmbeddingInserter 类")
    else:
        print("\n❌ 仍有问题需要解决")
