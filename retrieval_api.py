# retrieval_api.py
from fastapi import FastAPI, File, UploadFile, HTTPException, Query
from fastapi.responses import JSONResponse
from multimodal_retrieval_system import MultimodalRetrievalSystem
from clip_encoder import ChineseClipEncoder
from multimodal_vector_db_manager import MilvusMultimodalManager
import uvicorn
import os
from io import BytesIO
from PIL import Image
# 初始化模型和数据库
def create_retrieval_system():
    """初始化检索系统核心组件"""
    try:
        clip_encoder = ChineseClipEncoder(model_name="ViT-B-16")
        milvus_manager = MilvusMultimodalManager(uri="http://localhost:19530")
        
        return MultimodalRetrievalSystem(
            milvus_manager.milvus_client,
            milvus_manager.collection_name,
            clip_encoder,
            results_dir="api_search_results"
        )
    except Exception as e:
        raise RuntimeError(f"初始化检索系统失败: {str(e)}")

# 创建FastAPI应用
app = FastAPI(
    title="多模态检索系统API",
    description="支持文本到图像和图像到图像的相似性搜索",
    version="1.0.0"
)

# 初始化全局检索实例
retrieval_system = create_retrieval_system()

@app.get("/")
def read_root():
    """健康检查端点"""
    return {"status": "服务正常运行", "version": "1.0.0"}

@app.post("/text_search")
async def text_to_image_search(
    query_text: str = Query(..., description="文本查询内容", min_length=1),
    limit: int = Query(10, ge=1, le=100, description="返回结果数量"),
    grid_size: str = Query("2x2", pattern=r"^\d+x\d+$", description="结果网格尺寸（如2x2）")
):
    """
    文本到图像检索接口
    示例: /text_search?query_text=夕阳&limit=4&grid_size=2x2
    """
    try:
        # 解析网格尺寸
        rows, cols = map(int, grid_size.split('x'))
        
        # 执行搜索
        save_info = retrieval_system.visualize_and_save_search_results(
            query_text=query_text,
            results=None,
            grid_size=(rows, cols),
            image_size=(400, 400),
            prefix="text_query"
        )
        
        return JSONResponse(content={
            "status": "success",
            "query_text": query_text,
            "result_count": limit,
            "results_grid": save_info['grid_path'],
            "individual_results_dir": save_info['results_dir']
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文本搜索失败: {str(e)}")

@app.post("/image_search")
async def image_to_image_search(
    file: UploadFile = File(..., description="上传查询图像文件"),
    limit: int = Query(10, ge=1, le=100, description="返回结果数量"),
    grid_size: str = Query("2x2", pattern=r"^\d+x\d+$", description="结果网格尺寸")
):
    """
    图像到图像检索接口
    上传图像文件进行相似图片搜索
    """
    # 一次性读取文件内容
    try:
        contents = await file.read()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件读取失败: {str(e)}")
    
    # 验证文件类型
    valid_content_type = file.content_type
    try:
        img = Image.open(BytesIO(contents))
        img.verify()
        ext = img.format.lower() if img.format else "jpeg"
        valid_content_type = f"image/{ext}"
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"无效的图像文件: {str(e)}")
    
    # 创建临时文件存储目录
    temp_dir = "api_temp_uploads"
    os.makedirs(temp_dir, exist_ok=True)
    
    temp_path = None
    try:
        # 生成唯一的临时文件名
        import uuid
        file_id = str(uuid.uuid4())
        _, orig_ext = os.path.splitext(file.filename)
        orig_ext = orig_ext.lower() or ".jpg"
        
        temp_path = os.path.join(temp_dir, f"{file_id}{orig_ext}")
        
        # 保存文件
        with open(temp_path, "wb") as buffer:
            buffer.write(contents)
        
        # 解析网格尺寸
        rows, cols = map(int, grid_size.split('x'))
        
        # 执行搜索
        save_info = retrieval_system.visualize_and_save_search_results(
            query_image_path=temp_path,
            results=None,
            grid_size=(rows, cols),
            image_size=(400, 400),
            prefix="image_query"
        )
        
        return JSONResponse(content={
            "status": "success",
            "query_image": file.filename,
            "result_count": limit,
            "results_grid": save_info['grid_path'],
            "individual_results_dir": save_info['results_dir']
        })
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图像搜索失败: {str(e)}")
    
    finally:
        # 确保清理临时文件
        if temp_path and os.path.exists(temp_path):
            try:
                os.remove(temp_path)
            except Exception as e:
                print(f"临时文件清理失败: {str(e)}")
# ... existing code ...
# 启动服务
if __name__ == "__main__":
    uvicorn.run("retrieval_api:app", host="0.0.0.0", port=8900, reload=True)