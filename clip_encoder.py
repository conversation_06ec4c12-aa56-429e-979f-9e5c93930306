"""
共享CLIP编码器模块
支持中文CLIP模型的文本和图像编码功能
"""
import cn_clip.clip as clip
from cn_clip.clip import available_models
import torch
from PIL import Image
import warnings
import shutil
import os
from tqdm import tqdm  # 用于显示进度条
class ChineseClipEncoder:
    def __init__(self, model_name="ViT-B-16", device=None, download_root='./chinese_clip_model'):
        """
        初始化中文CLIP模型
        
        参数:
        model_name: 模型名称，默认为"ViT-B-16"
        device: 指定计算设备(cuda/cpu)，默认自动选择
        download_root: 模型下载目录
        """
        # 设置设备
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        print(self.device)
        self.model_name = model_name
        
        # 验证模型是否可用
        if model_name not in available_models():
            warnings.warn(f"模型 {model_name} 不在可用列表中! 可用模型: {available_models()}")
        
        # 加载模型和预处理函数
        self.model, self.preprocess = clip.load_from_name(
            model_name, 
            device=self.device, 
            download_root=download_root
        )
        self.model.eval()
        print(f"✓ 模型加载成功: {model_name} | 设备: {self.device}")
        print("-" * 50)
    def classify_image(self, image_path, candidate_labels,confidence_threshold):
        """
        对单张图像进行分类
        
        参数:
            image_path: 图像文件路径
            candidate_labels: 候选标签列表 (中文)
            
        返回:
            str: 预测的标签 (如果置信度低于阈值则返回"uncertain")
            float: 对应的概率
        """
        # 加载和预处理图像
        image = self.preprocess(Image.open(image_path)).unsqueeze(0).to(self.device)
        
        # 处理文本标签
        # text_inputs = clip.tokenize([f"这是一个{label}" for label in candidate_labels]).to(self.device)
        text_inputs = clip.tokenize([f"{label}" for label in candidate_labels]).to(self.device)

        with torch.no_grad():
            logits_per_image, logits_per_text = self.model.get_similarity(image, text_inputs)
            probs = logits_per_image.softmax(dim=-1).cpu().numpy()
            # probs = (logits_per_image * 100).softmax(dim=-1).cpu().numpy()
        # 获取最高概率标签
        max_idx = probs.argmax()
        max_prob = probs[0,max_idx]
        max_label = candidate_labels[max_idx]
        print(f"{image_path} | 预测标签: {max_label} | 概率: {max_prob:.4f}")
        # 检查置信度
        if max_prob < confidence_threshold:
            return "uncertain", max_prob
        return max_label, max_prob
    
    def classify_and_organize_images(self, source_dir, dest_dir, candidate_labels, confidence_threshold=0.5,
                                    copy_files=True, log_file="classification_log.txt"):
        """
        分类并组织图像文件
        
        参数:
            source_dir: 源图像文件夹路径
            dest_dir: 目标文件夹路径
            candidate_labels: 候选标签列表 (中文)
            copy_files: True=复制文件, False=移动文件
            log_file: 日志文件名
        """
        # 支持的图像格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        # 创建目标目录
        os.makedirs(dest_dir, exist_ok=True)
        
        # 创建所有标签文件夹
        all_labels = list(candidate_labels) + ["uncertain"]
        for label in all_labels:
            os.makedirs(os.path.join(dest_dir, label), exist_ok=True)
        
        # 获取所有图像文件
        image_files = []
        for root, _, files in os.walk(source_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    image_files.append(os.path.join(root, file))
        
        # 创建日志文件
        log_path = os.path.join(dest_dir, log_file)
        with open(log_path, "w", encoding="utf-8") as log:
            log.write("文件路径\t预测标签\t置信度\n")
            
            # 处理每张图像
            for image_path in tqdm(image_files, desc="分类处理图像"):
                try:
                    # 分类图像
                    label, confidence = self.classify_image(image_path, candidate_labels,confidence_threshold)
                    
                    # 目标路径
                    dest_folder = os.path.join(dest_dir, label)
                    dest_path = os.path.join(dest_folder, os.path.basename(image_path))
                    
                    # 复制或移动文件
                    if copy_files:
                        shutil.copy2(image_path, dest_path)
                    else:
                        shutil.move(image_path, dest_path)
                    
                    # 记录日志
                    log.write(f"{image_path}\t{label}\t{confidence:.4f}\n")
                    
                except Exception as e:
                    print(f"处理图像 {image_path} 时出错: {str(e)}")
                    log.write(f"{image_path}\tERROR\t{str(e)}\n")
        
        print(f"处理完成! 共处理 {len(image_files)} 张图像")
        print(f"日志文件已保存至: {log_path}")


    def encode_image(self, image_input):
        """
        对图像进行向量化编码
        
        参数:
        image_input: 可以是图片路径(PIL.Image)或已打开的PIL图像对象
        
        返回:
        归一化后的图像特征向量(list)
        """
        # 支持文件路径或PIL图像对象
        if isinstance(image_input, str):
            try:
                image = Image.open(image_input).convert('RGB')
            except Exception as e:
                raise ValueError(f"无法打开图像: {image_input} | 错误: {str(e)}")
        elif isinstance(image_input, Image.Image):
            image = image_input.convert('RGB')
        else:
            raise TypeError("不支持的图像输入类型，请提供路径字符串或PIL图像对象")
        
        with torch.no_grad():
            processed_image = self.preprocess(image).unsqueeze(0).to(self.device)
            image_features = self.model.encode_image(processed_image)
            image_features /= image_features.norm(dim=-1, keepdim=True)
            return image_features.squeeze().cpu().numpy().tolist()
    
    def encode_text(self, text_input):
        """
        对文本进行向量化编码
        
        参数:
        text_input: 字符串或字符串列表(支持批量编码)
        
        返回:
        单个文本的特征向量(list)或批量文本的特征向量列表
        """
        # 统一处理为列表格式
        text_list = [text_input] if isinstance(text_input, str) else text_input
        
        if not isinstance(text_list, list) or not all(isinstance(t, str) for t in text_list):
            raise TypeError("文本输入必须是字符串或字符串列表")
        
        with torch.no_grad():
            text_tokens = clip.tokenize(text_list).to(self.device)
            text_features = self.model.encode_text(text_tokens)
            text_features /= text_features.norm(dim=-1, keepdim=True)
            features_list = [f.squeeze().cpu().numpy().tolist() for f in text_features]
            
            # 单个文本直接返回向量，多个文本返回列表
            return features_list[0] if isinstance(text_input, str) else features_list
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "embedding_dim": self.model.text_projection.shape[-1]
        }
    

if __name__ == "__main__":
    # 测试模型加载和编码功能
    # 初始化编码器
    encoder = ChineseClipEncoder(model_name="ViT-B-16")

    # 获取模型信息
    print("模型信息:", encoder.get_model_info())

    # 编码单个图像
    image_vector = encoder.encode_image("./images_data/2007_007084.jpg")
    print("图像向量维度:", len(image_vector),image_vector)

    # # 编码单个文本
    text_vector = encoder.encode_text("一只可爱的猫咪")
    print("文本向量维度:", len(text_vector))

    # # 批量编码文本
    texts = ["蓝天白云", "城市夜景", "自然风光"]
    batch_vectors = encoder.encode_text(texts)
    print("批量文本向量数量:", len(batch_vectors))


    #图像分类
    # 定义候选标签 (必须使用中文)
    labels = [ "猫", "火车", "飞机", "人物", "山","电脑", "自行车","公园","狗","汽车","摩托车","城市","花","草地","天空","海洋"]

    # 设置源文件夹和目标文件夹
    # source_directory = "../JPEGImages"  # 替换为你的图像文件夹路径
    # target_directory = "../JPEGImages_autoclass_v4"  # 替换为分类后的目标文件夹路径

    # encoder.classify_and_organize_images(
    #     source_dir=source_directory,
    #     dest_dir=target_directory,
    #     candidate_labels=labels,
    #     copy_files=True  # True=复制文件, False=移动文件(会删除原文件)
    # )