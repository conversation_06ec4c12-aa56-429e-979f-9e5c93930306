#!/usr/bin/env python3
"""
测试flush方法修复
"""

from pymilvus import MilvusClient
import time

def test_flush_methods():
    """测试不同版本的flush方法"""
    print("=== 测试flush方法 ===\n")
    
    client = MilvusClient(uri="http://localhost:19530")
    collection_name = "multimodal_chinese_clip"
    
    print("1. 测试新版本flush方法 (collection_name)...")
    try:
        client.flush(collection_name=collection_name)
        print("✓ 新版本flush方法成功")
        return "new"
    except TypeError as e:
        print(f"✗ 新版本flush方法失败: {e}")
        
    print("\n2. 测试旧版本flush方法 (collection_names)...")
    try:
        client.flush(collection_names=[collection_name])
        print("✓ 旧版本flush方法成功")
        return "old"
    except Exception as e:
        print(f"✗ 旧版本flush方法也失败: {e}")
        return "none"

def check_stats_after_flush():
    """刷新后检查统计信息"""
    print("\n=== 刷新后检查统计信息 ===\n")
    
    client = MilvusClient(uri="http://localhost:19530")
    collection_name = "multimodal_chinese_clip"
    
    # 获取刷新前统计
    stats_before = client.get_collection_stats(collection_name)
    print(f"刷新前统计: {stats_before}")
    
    # 执行刷新
    flush_method = test_flush_methods()
    
    if flush_method != "none":
        print(f"\n3. 等待统计信息更新...")
        for i in range(10):
            time.sleep(1)
            stats = client.get_collection_stats(collection_name)
            print(f"第{i+1}秒: {stats}")
            
            # 如果统计信息更新了，就停止等待
            if stats.get('row_count', 0) != stats_before.get('row_count', 0):
                print(f"✓ 统计信息已更新！")
                break
        
        # 最终统计
        final_stats = client.get_collection_stats(collection_name)
        print(f"\n最终统计: {final_stats}")
        
        if final_stats.get('row_count', 0) > 0:
            print(f"🎉 成功！集合中有 {final_stats['row_count']} 条记录")
        else:
            print("⚠ 统计信息仍显示为0，但数据可能已存在")
            
            # 尝试查询验证
            try:
                results = client.query(
                    collection_name=collection_name,
                    filter="",
                    output_fields=["id"],
                    limit=1
                )
                if results:
                    print(f"✓ 查询验证：数据确实存在（找到记录ID: {results[0]['id']}）")
                else:
                    print("✗ 查询验证：没有找到任何记录")
            except Exception as e:
                print(f"查询验证失败: {e}")

if __name__ == "__main__":
    check_stats_after_flush()
