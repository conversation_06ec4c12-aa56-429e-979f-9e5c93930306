#!/usr/bin/env python3
"""
快速测试修复后的功能
"""

from multimodal_vector_db_manager import MilvusMultimodalManager

def main():
    print("=== 快速测试集合设置 ===\n")
    
    # 初始化管理器
    milvus_manager = MilvusMultimodalManager(
        uri="http://localhost:19530",
        collection_name="multimodal_chinese_clip"
    )
    
    # 测试setup_collection方法
    print("测试 setup_collection(recreate=False)...")
    result = milvus_manager.setup_collection(recreate=False)
    
    print(f"\n结果: {'✓ 成功' if result else '✗ 失败'}")
    
    if result:
        print("🎉 集合设置成功！现在可以正常使用了。")
        
        # 显示集合状态
        stats = milvus_manager.get_collection_stats()
        print(f"集合统计: {stats}")
        
        load_state = milvus_manager.milvus_client.get_load_state(
            collection_name=milvus_manager.collection_name
        )
        print(f"加载状态: {load_state}")
        
    else:
        print("❌ 集合设置失败，请检查错误信息")

if __name__ == "__main__":
    main()
