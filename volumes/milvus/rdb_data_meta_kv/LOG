2025/08/21-03:29:41.828662 43 RocksDB version: 6.29.5
2025/08/21-03:29:41.828761 43 Git sha 0
2025/08/21-03:29:41.828767 43 Compile date 2024-11-15 11:22:58
2025/08/21-03:29:41.828780 43 DB SUMMARY
2025/08/21-03:29:41.828783 43 DB Session ID:  5W38OURM5UC2K53C46VG
2025/08/21-03:29:41.828814 43 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/08/21-03:29:41.828821 43 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 
2025/08/21-03:29:41.828826 43                         Options.error_if_exists: 0
2025/08/21-03:29:41.828830 43                       Options.create_if_missing: 1
2025/08/21-03:29:41.828832 43                         Options.paranoid_checks: 1
2025/08/21-03:29:41.828834 43             Options.flush_verify_memtable_count: 1
2025/08/21-03:29:41.828836 43                               Options.track_and_verify_wals_in_manifest: 0
2025/08/21-03:29:41.828838 43                                     Options.env: 0x72ba73b16d00
2025/08/21-03:29:41.828841 43                                      Options.fs: PosixFileSystem
2025/08/21-03:29:41.828843 43                                Options.info_log: 0x72b908490050
2025/08/21-03:29:41.828845 43                Options.max_file_opening_threads: 16
2025/08/21-03:29:41.828846 43                              Options.statistics: (nil)
2025/08/21-03:29:41.828849 43                               Options.use_fsync: 0
2025/08/21-03:29:41.828850 43                       Options.max_log_file_size: 0
2025/08/21-03:29:41.828852 43                  Options.max_manifest_file_size: 1073741824
2025/08/21-03:29:41.828854 43                   Options.log_file_time_to_roll: 0
2025/08/21-03:29:41.828856 43                       Options.keep_log_file_num: 1000
2025/08/21-03:29:41.828858 43                    Options.recycle_log_file_num: 0
2025/08/21-03:29:41.828860 43                         Options.allow_fallocate: 1
2025/08/21-03:29:41.828862 43                        Options.allow_mmap_reads: 0
2025/08/21-03:29:41.828864 43                       Options.allow_mmap_writes: 0
2025/08/21-03:29:41.828866 43                        Options.use_direct_reads: 0
2025/08/21-03:29:41.828867 43                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/21-03:29:41.828869 43          Options.create_missing_column_families: 0
2025/08/21-03:29:41.828871 43                              Options.db_log_dir: 
2025/08/21-03:29:41.828873 43                                 Options.wal_dir: 
2025/08/21-03:29:41.828875 43                Options.table_cache_numshardbits: 6
2025/08/21-03:29:41.828877 43                         Options.WAL_ttl_seconds: 0
2025/08/21-03:29:41.828878 43                       Options.WAL_size_limit_MB: 0
2025/08/21-03:29:41.828880 43                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/21-03:29:41.828882 43             Options.manifest_preallocation_size: 4194304
2025/08/21-03:29:41.828884 43                     Options.is_fd_close_on_exec: 1
2025/08/21-03:29:41.828886 43                   Options.advise_random_on_open: 1
2025/08/21-03:29:41.828888 43                   Options.experimental_mempurge_threshold: 0.000000
2025/08/21-03:29:41.829088 43                    Options.db_write_buffer_size: 0
2025/08/21-03:29:41.829092 43                    Options.write_buffer_manager: 0x72b9174600a0
2025/08/21-03:29:41.829094 43         Options.access_hint_on_compaction_start: 1
2025/08/21-03:29:41.829096 43  Options.new_table_reader_for_compaction_inputs: 0
2025/08/21-03:29:41.829105 43           Options.random_access_max_buffer_size: 1048576
2025/08/21-03:29:41.829108 43                      Options.use_adaptive_mutex: 0
2025/08/21-03:29:41.829110 43                            Options.rate_limiter: (nil)
2025/08/21-03:29:41.829115 43     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/21-03:29:41.829117 43                       Options.wal_recovery_mode: 2
2025/08/21-03:29:41.829119 43                  Options.enable_thread_tracking: 0
2025/08/21-03:29:41.829120 43                  Options.enable_pipelined_write: 0
2025/08/21-03:29:41.829122 43                  Options.unordered_write: 0
2025/08/21-03:29:41.829143 43         Options.allow_concurrent_memtable_write: 1
2025/08/21-03:29:41.829146 43      Options.enable_write_thread_adaptive_yield: 1
2025/08/21-03:29:41.829148 43             Options.write_thread_max_yield_usec: 100
2025/08/21-03:29:41.829149 43            Options.write_thread_slow_yield_usec: 3
2025/08/21-03:29:41.829151 43                               Options.row_cache: None
2025/08/21-03:29:41.829153 43                              Options.wal_filter: None
2025/08/21-03:29:41.829155 43             Options.avoid_flush_during_recovery: 0
2025/08/21-03:29:41.829157 43             Options.allow_ingest_behind: 0
2025/08/21-03:29:41.829159 43             Options.preserve_deletes: 0
2025/08/21-03:29:41.829160 43             Options.two_write_queues: 0
2025/08/21-03:29:41.829162 43             Options.manual_wal_flush: 0
2025/08/21-03:29:41.829164 43             Options.atomic_flush: 0
2025/08/21-03:29:41.829166 43             Options.avoid_unnecessary_blocking_io: 0
2025/08/21-03:29:41.829168 43                 Options.persist_stats_to_disk: 0
2025/08/21-03:29:41.829169 43                 Options.write_dbid_to_manifest: 0
2025/08/21-03:29:41.829171 43                 Options.log_readahead_size: 0
2025/08/21-03:29:41.829173 43                 Options.file_checksum_gen_factory: Unknown
2025/08/21-03:29:41.829175 43                 Options.best_efforts_recovery: 0
2025/08/21-03:29:41.829177 43                Options.max_bgerror_resume_count: 2147483647
2025/08/21-03:29:41.829179 43            Options.bgerror_resume_retry_interval: 1000000
2025/08/21-03:29:41.829180 43             Options.allow_data_in_errors: 0
2025/08/21-03:29:41.829182 43             Options.db_host_id: __hostname__
2025/08/21-03:29:41.829186 43             Options.max_background_jobs: 2
2025/08/21-03:29:41.829188 43             Options.max_background_compactions: -1
2025/08/21-03:29:41.829190 43             Options.max_subcompactions: 1
2025/08/21-03:29:41.829192 43             Options.avoid_flush_during_shutdown: 0
2025/08/21-03:29:41.829193 43           Options.writable_file_max_buffer_size: 1048576
2025/08/21-03:29:41.829195 43             Options.delayed_write_rate : 16777216
2025/08/21-03:29:41.829197 43             Options.max_total_wal_size: 0
2025/08/21-03:29:41.829199 43             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/21-03:29:41.829201 43                   Options.stats_dump_period_sec: 600
2025/08/21-03:29:41.829203 43                 Options.stats_persist_period_sec: 600
2025/08/21-03:29:41.829204 43                 Options.stats_history_buffer_size: 1048576
2025/08/21-03:29:41.829206 43                          Options.max_open_files: -1
2025/08/21-03:29:41.829208 43                          Options.bytes_per_sync: 0
2025/08/21-03:29:41.829210 43                      Options.wal_bytes_per_sync: 0
2025/08/21-03:29:41.829212 43                   Options.strict_bytes_per_sync: 0
2025/08/21-03:29:41.829214 43       Options.compaction_readahead_size: 0
2025/08/21-03:29:41.829215 43                  Options.max_background_flushes: 1
2025/08/21-03:29:41.829217 43 Compression algorithms supported:
2025/08/21-03:29:41.829220 43 	kZSTD supported: 1
2025/08/21-03:29:41.829222 43 	kXpressCompression supported: 0
2025/08/21-03:29:41.829224 43 	kBZip2Compression supported: 0
2025/08/21-03:29:41.829226 43 	kZSTDNotFinalCompression supported: 1
2025/08/21-03:29:41.829228 43 	kLZ4Compression supported: 0
2025/08/21-03:29:41.829234 43 	kZlibCompression supported: 0
2025/08/21-03:29:41.829236 43 	kLZ4HCCompression supported: 0
2025/08/21-03:29:41.829238 43 	kSnappyCompression supported: 0
2025/08/21-03:29:41.829243 43 Fast CRC32 supported: Not supported on x86
2025/08/21-03:29:41.833197 43 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/08/21-03:29:41.839499 43 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001
2025/08/21-03:29:41.840078 43 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/21-03:29:41.840186 43               Options.comparator: leveldb.BytewiseComparator
2025/08/21-03:29:41.840201 43           Options.merge_operator: None
2025/08/21-03:29:41.840208 43        Options.compaction_filter: None
2025/08/21-03:29:41.840210 43        Options.compaction_filter_factory: None
2025/08/21-03:29:41.840213 43  Options.sst_partitioner_factory: None
2025/08/21-03:29:41.840216 43         Options.memtable_factory: SkipListFactory
2025/08/21-03:29:41.840219 43            Options.table_factory: BlockBasedTable
2025/08/21-03:29:41.840338 43            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x72b917400100)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x72b917460010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2001500897
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/21-03:29:41.840346 43        Options.write_buffer_size: 67108864
2025/08/21-03:29:41.840349 43  Options.max_write_buffer_number: 2
2025/08/21-03:29:41.840354 43        Options.compression[0]: NoCompression
2025/08/21-03:29:41.840357 43        Options.compression[1]: NoCompression
2025/08/21-03:29:41.840360 43        Options.compression[2]: ZSTD
2025/08/21-03:29:41.840362 43        Options.compression[3]: ZSTD
2025/08/21-03:29:41.840364 43        Options.compression[4]: ZSTD
2025/08/21-03:29:41.840367 43                  Options.bottommost_compression: Disabled
2025/08/21-03:29:41.840369 43       Options.prefix_extractor: nullptr
2025/08/21-03:29:41.840371 43   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/21-03:29:41.840374 43             Options.num_levels: 5
2025/08/21-03:29:41.840376 43        Options.min_write_buffer_number_to_merge: 1
2025/08/21-03:29:41.840378 43     Options.max_write_buffer_number_to_maintain: 0
2025/08/21-03:29:41.840380 43     Options.max_write_buffer_size_to_maintain: 0
2025/08/21-03:29:41.840383 43            Options.bottommost_compression_opts.window_bits: -14
2025/08/21-03:29:41.840385 43                  Options.bottommost_compression_opts.level: 32767
2025/08/21-03:29:41.840388 43               Options.bottommost_compression_opts.strategy: 0
2025/08/21-03:29:41.840390 43         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/21-03:29:41.840392 43         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/21-03:29:41.840394 43         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/21-03:29:41.840397 43                  Options.bottommost_compression_opts.enabled: false
2025/08/21-03:29:41.840399 43         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/21-03:29:41.840402 43            Options.compression_opts.window_bits: -14
2025/08/21-03:29:41.840404 43                  Options.compression_opts.level: 32767
2025/08/21-03:29:41.840406 43               Options.compression_opts.strategy: 0
2025/08/21-03:29:41.840408 43         Options.compression_opts.max_dict_bytes: 0
2025/08/21-03:29:41.840411 43         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/21-03:29:41.840413 43         Options.compression_opts.parallel_threads: 1
2025/08/21-03:29:41.840415 43                  Options.compression_opts.enabled: false
2025/08/21-03:29:41.840438 43         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/21-03:29:41.840440 43      Options.level0_file_num_compaction_trigger: 4
2025/08/21-03:29:41.840443 43          Options.level0_slowdown_writes_trigger: 20
2025/08/21-03:29:41.840445 43              Options.level0_stop_writes_trigger: 36
2025/08/21-03:29:41.840448 43                   Options.target_file_size_base: 67108864
2025/08/21-03:29:41.840450 43             Options.target_file_size_multiplier: 2
2025/08/21-03:29:41.840452 43                Options.max_bytes_for_level_base: 268435456
2025/08/21-03:29:41.840455 43 Options.level_compaction_dynamic_level_bytes: 0
2025/08/21-03:29:41.840457 43          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/21-03:29:41.840462 43 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/21-03:29:41.840465 43 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/21-03:29:41.840468 43 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/21-03:29:41.840470 43 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/21-03:29:41.840472 43 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/21-03:29:41.840474 43 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/21-03:29:41.840476 43 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/21-03:29:41.840479 43       Options.max_sequential_skip_in_iterations: 8
2025/08/21-03:29:41.840481 43                    Options.max_compaction_bytes: 1677721600
2025/08/21-03:29:41.840483 43                        Options.arena_block_size: 1048576
2025/08/21-03:29:41.840486 43   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/21-03:29:41.840488 43   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/21-03:29:41.840491 43       Options.rate_limit_delay_max_milliseconds: 100
2025/08/21-03:29:41.840493 43                Options.disable_auto_compactions: 0
2025/08/21-03:29:41.840499 43                        Options.compaction_style: kCompactionStyleLevel
2025/08/21-03:29:41.840502 43                          Options.compaction_pri: kMinOverlappingRatio
2025/08/21-03:29:41.840504 43 Options.compaction_options_universal.size_ratio: 1
2025/08/21-03:29:41.840506 43 Options.compaction_options_universal.min_merge_width: 2
2025/08/21-03:29:41.840508 43 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/21-03:29:41.840511 43 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/21-03:29:41.840513 43 Options.compaction_options_universal.compression_size_percent: -1
2025/08/21-03:29:41.840516 43 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/21-03:29:41.840518 43 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/21-03:29:41.840520 43 Options.compaction_options_fifo.allow_compaction: 0
2025/08/21-03:29:41.840529 43                   Options.table_properties_collectors: 
2025/08/21-03:29:41.840532 43                   Options.inplace_update_support: 0
2025/08/21-03:29:41.840534 43                 Options.inplace_update_num_locks: 10000
2025/08/21-03:29:41.840537 43               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/21-03:29:41.840540 43               Options.memtable_whole_key_filtering: 0
2025/08/21-03:29:41.840543 43   Options.memtable_huge_page_size: 0
2025/08/21-03:29:41.840545 43                           Options.bloom_locality: 0
2025/08/21-03:29:41.840547 43                    Options.max_successive_merges: 0
2025/08/21-03:29:41.840549 43                Options.optimize_filters_for_hits: 0
2025/08/21-03:29:41.840551 43                Options.paranoid_file_checks: 0
2025/08/21-03:29:41.840553 43                Options.force_consistency_checks: 1
2025/08/21-03:29:41.840556 43                Options.report_bg_io_stats: 0
2025/08/21-03:29:41.840558 43                               Options.ttl: 2592000
2025/08/21-03:29:41.840560 43          Options.periodic_compaction_seconds: 0
2025/08/21-03:29:41.840562 43                       Options.enable_blob_files: false
2025/08/21-03:29:41.840574 43                           Options.min_blob_size: 0
2025/08/21-03:29:41.840577 43                          Options.blob_file_size: 268435456
2025/08/21-03:29:41.840579 43                   Options.blob_compression_type: NoCompression
2025/08/21-03:29:41.840582 43          Options.enable_blob_garbage_collection: false
2025/08/21-03:29:41.840584 43      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/21-03:29:41.840587 43 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/21-03:29:41.840589 43          Options.blob_compaction_readahead_size: 0
2025/08/21-03:29:41.843442 43 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/21-03:29:41.843453 43 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/21-03:29:41.843606 43 [db/version_set.cc:4409] Creating manifest 4
2025/08/21-03:29:41.855579 43 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x72b908580000
2025/08/21-03:29:41.855619 43 DB pointer 0x72b908420000
2025/08/21-03:29:41.856656 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-03:29:41.856729 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 1 last_copies: 0 last_secs: 0.000217 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-03:39:41.857521 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-03:39:41.857597 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 4899 writes, 4916 keys, 4518 commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 4899 writes, 0 syncs, 4899.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 4899 writes, 4916 keys, 4518 commit groups, 1.1 writes per commit group, ingest: 0.25 MB, 0.00 MB/s
Interval WAL: 4899 writes, 0 syncs, 4899.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 2 last_copies: 0 last_secs: 0.000178 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-03:49:41.858162 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-03:49:41.858252 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.0 total, 600.0 interval
Cumulative writes: 10K writes, 10K keys, 10K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 10K writes, 0 syncs, 10899.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5626 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 3 last_copies: 0 last_secs: 0.000133 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-04:14:38.694444 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-04:14:38.694477 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2696.9 total, 1496.8 interval
Cumulative writes: 14K writes, 14K keys, 13K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14K writes, 0 syncs, 14107.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3208 writes, 3208 keys, 3019 commit groups, 1.1 writes per commit group, ingest: 0.17 MB, 0.00 MB/s
Interval WAL: 3208 writes, 0 syncs, 3208.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2696.9 total, 1496.8 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 4 last_copies: 0 last_secs: 0.000154 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-04:24:38.694861 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-04:24:38.694940 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3296.9 total, 600.0 interval
Cumulative writes: 20K writes, 20K keys, 18K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 20K writes, 0 syncs, 20125.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6018 writes, 6018 keys, 5034 commit groups, 1.2 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6018 writes, 0 syncs, 6018.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3296.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 5 last_copies: 0 last_secs: 9.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-04:34:38.695320 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-04:34:38.695384 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3896.9 total, 600.0 interval
Cumulative writes: 26K writes, 26K keys, 23K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 26K writes, 0 syncs, 26143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6018 writes, 6018 keys, 5472 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6018 writes, 0 syncs, 6018.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3896.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 6 last_copies: 0 last_secs: 9.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-04:44:38.695881 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-04:44:38.695968 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4496.9 total, 600.0 interval
Cumulative writes: 32K writes, 32K keys, 29K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 32K writes, 0 syncs, 32143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5458 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4496.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 7 last_copies: 0 last_secs: 0.000121 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-04:54:38.696489 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-04:54:38.696577 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5096.9 total, 600.0 interval
Cumulative writes: 38K writes, 38K keys, 34K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 38K writes, 0 syncs, 38143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5509 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5096.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 8 last_copies: 0 last_secs: 0.00015 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-05:04:38.697203 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-05:04:38.697283 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5696.9 total, 600.0 interval
Cumulative writes: 44K writes, 44K keys, 40K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 44K writes, 0 syncs, 44143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5831 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5696.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 9 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-05:14:38.697756 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-05:14:38.697825 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6296.9 total, 600.0 interval
Cumulative writes: 50K writes, 50K keys, 46K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 50K writes, 0 syncs, 50143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5692 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6296.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 10 last_copies: 0 last_secs: 0.000114 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-05:24:38.698341 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-05:24:38.698420 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6896.9 total, 600.0 interval
Cumulative writes: 56K writes, 56K keys, 51K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 56K writes, 0 syncs, 56143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5743 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6896.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 11 last_copies: 0 last_secs: 0.000124 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-05:34:38.699005 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-05:34:38.699082 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7496.9 total, 600.0 interval
Cumulative writes: 62K writes, 62K keys, 57K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 62K writes, 0 syncs, 62143.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5819 commit groups, 1.0 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7496.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 12 last_copies: 0 last_secs: 0.000128 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-06:03:13.700243 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-06:03:13.700345 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9211.9 total, 1715.0 interval
Cumulative writes: 64K writes, 64K keys, 59K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 64K writes, 0 syncs, 64171.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2028 writes, 2028 keys, 1952 commit groups, 1.0 writes per commit group, ingest: 0.11 MB, 0.00 MB/s
Interval WAL: 2028 writes, 0 syncs, 2028.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9211.9 total, 1715.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 13 last_copies: 0 last_secs: 0.000152 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-06:13:13.700770 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-06:13:13.700853 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9811.9 total, 600.0 interval
Cumulative writes: 70K writes, 70K keys, 65K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 70K writes, 0 syncs, 70205.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6034 writes, 6034 keys, 5559 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6034 writes, 0 syncs, 6034.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9811.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 14 last_copies: 0 last_secs: 0.000119 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-06:23:13.701209 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-06:23:13.701267 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 10411.9 total, 600.0 interval
Cumulative writes: 76K writes, 76K keys, 70K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 76K writes, 0 syncs, 76197.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5992 writes, 5992 keys, 5511 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 5992 writes, 0 syncs, 5992.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 10411.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 15 last_copies: 0 last_secs: 9.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-06:33:13.701450 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-06:33:13.701471 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11011.9 total, 600.0 interval
Cumulative writes: 82K writes, 82K keys, 76K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 82K writes, 0 syncs, 82197.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5561 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11011.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 16 last_copies: 0 last_secs: 3.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-06:43:13.702101 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-06:43:13.702180 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 11611.9 total, 600.0 interval
Cumulative writes: 88K writes, 88K keys, 82K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 88K writes, 0 syncs, 88197.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5712 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 11611.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 17 last_copies: 0 last_secs: 0.000119 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-07:01:05.717430 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-07:01:05.717468 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 12683.9 total, 1072.0 interval
Cumulative writes: 93K writes, 93K keys, 87K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 93K writes, 0 syncs, 93407.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 5210 writes, 5210 keys, 5016 commit groups, 1.0 writes per commit group, ingest: 0.28 MB, 0.00 MB/s
Interval WAL: 5210 writes, 0 syncs, 5210.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 12683.9 total, 1072.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 18 last_copies: 0 last_secs: 0.000148 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:08:55.702970 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:08:55.702982 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20353.9 total, 7670.0 interval
Cumulative writes: 94K writes, 94K keys, 87K commit groups, 1.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 94K writes, 0 syncs, 94025.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 618 writes, 618 keys, 591 commit groups, 1.0 writes per commit group, ingest: 0.03 MB, 0.00 MB/s
Interval WAL: 618 writes, 0 syncs, 618.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20353.9 total, 7670.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 19 last_copies: 0 last_secs: 4.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:18:55.703618 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:18:55.703742 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 20953.9 total, 600.0 interval
Cumulative writes: 100K writes, 100K keys, 93K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 100K writes, 0 syncs, 100027.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6002 writes, 6002 keys, 5591 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6002 writes, 0 syncs, 6002.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 20953.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 20 last_copies: 0 last_secs: 0.000135 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:28:55.704251 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:28:55.704341 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 21553.9 total, 600.0 interval
Cumulative writes: 106K writes, 106K keys, 98K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 106K writes, 0 syncs, 106027.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5551 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 21553.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 21 last_copies: 0 last_secs: 0.000125 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:38:55.705029 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:38:55.705126 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22153.9 total, 600.0 interval
Cumulative writes: 112K writes, 112K keys, 104K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 112K writes, 0 syncs, 112027.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6000 writes, 6000 keys, 5502 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6000 writes, 0 syncs, 6000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22153.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 22 last_copies: 0 last_secs: 0.000134 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:48:55.705669 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:48:55.705894 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 22753.9 total, 600.0 interval
Cumulative writes: 118K writes, 118K keys, 109K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 118K writes, 0 syncs, 118082.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6055 writes, 6055 keys, 5088 commit groups, 1.2 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6055 writes, 0 syncs, 6055.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 22753.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 23 last_copies: 0 last_secs: 0.000118 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-09:58:55.706501 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-09:58:55.706583 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23353.9 total, 600.0 interval
Cumulative writes: 124K writes, 124K keys, 114K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 124K writes, 0 syncs, 124123.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6041 writes, 6041 keys, 5597 commit groups, 1.1 writes per commit group, ingest: 0.32 MB, 0.00 MB/s
Interval WAL: 6041 writes, 0 syncs, 6041.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23353.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 24 last_copies: 0 last_secs: 0.000134 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-10:08:55.707005 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-10:08:55.707072 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 23953.9 total, 600.0 interval
Cumulative writes: 130K writes, 130K keys, 120K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 130K writes, 0 syncs, 130166.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6043 writes, 6043 keys, 5305 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6043 writes, 0 syncs, 6043.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 23953.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 25 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-10:18:55.707643 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-10:18:55.707745 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 24553.9 total, 600.0 interval
Cumulative writes: 136K writes, 136K keys, 125K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 136K writes, 0 syncs, 136206.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6040 writes, 6040 keys, 5402 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6040 writes, 0 syncs, 6040.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 24553.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 26 last_copies: 0 last_secs: 0.000129 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-11:05:53.731227 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-11:05:53.731270 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27371.9 total, 2818.0 interval
Cumulative writes: 138K writes, 138K keys, 127K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 138K writes, 0 syncs, 138646.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2440 writes, 2440 keys, 2203 commit groups, 1.1 writes per commit group, ingest: 0.13 MB, 0.00 MB/s
Interval WAL: 2440 writes, 0 syncs, 2440.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27371.9 total, 2818.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 27 last_copies: 0 last_secs: 0.000146 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/21-11:15:53.731439 81 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/21-11:15:53.731463 81 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 27971.9 total, 600.0 interval
Cumulative writes: 144K writes, 144K keys, 133K commit groups, 1.1 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 144K writes, 0 syncs, 144648.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 6002 writes, 6002 keys, 5241 commit groups, 1.1 writes per commit group, ingest: 0.31 MB, 0.00 MB/s
Interval WAL: 6002 writes, 0 syncs, 6002.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 27971.9 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x72b917460010#8 capacity: 1.86 GB collections: 28 last_copies: 0 last_secs: 3.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
