# retrieval_ui.py
import gradio as gr
import requests
import os
import base64
from io import BytesIO
from PIL import Image

# FastAPI服务地址
FASTAPI_URL = "http://localhost:8900"

def search_by_text(query_text, limit=20):
    """通过文本搜索调用FastAPI接口"""
    try:
        response = requests.post(
            f"{FASTAPI_URL}/text_search",
            params={"query_text": query_text, "limit": limit, "grid_size": "5x4"}
        )
        if response.status_code == 200:
            data = response.json()
            print("data:", data)  # 调试输出
            return process_results(data)
        return f"搜索失败: {response.json()['detail']}"
    except Exception as e:
        return f"网络错误: {str(e)}"

def search_by_image(image, limit=20):
    """通过图像搜索调用FastAPI接口"""
    try:

         # 获取原始文件扩展名（如果存在）
        file_format = image.format or "JPEG"
        
        # 确保扩展名合法
        ext = file_format.lower() if file_format in ["JPEG", "PNG"] else "jpg"
        
        # 转换图片格式
        buffered = BytesIO()
        image.save(buffered, format=file_format)
        
        files = {"file": (f"query.{ext}", buffered.getvalue(),  f"image/{ext}")}
        response = requests.post(
            f"{FASTAPI_URL}/image_search",
            params={"limit": limit, "grid_size": "5x4"},
            files=files
        )
        
        if response.status_code == 200:
            data = response.json()
             # 确保data是字典且包含必要字段
            if isinstance(data, dict) and "individual_results_dir" in data:
                return process_results(data)
                
           
            return []
                
            # # 如果返回的是包含结果的列表
            # if isinstance(data, list):
            #     results_dir = os.path.join(os.path.dirname(data[0]["entity"]["filepath"]), "..")
            #     return process_results({
            #         "individual_results_dir": results_dir,
            #         "result_count": len(data)
            #     })
                
        return []  # 默认返回空列表保证数据格式一致性
        
    except Exception as e:
        print(f"图像搜索异常: {str(e)}")  # 添加详细日志
        return []


    
# ... existing code ...
def process_results(data):
    """处理搜索结果，加载图片和文件名"""
    if "individual_results_dir" not in data:
        return "无效的响应数据"
        
    result_dir = data["individual_results_dir"]
    image_files = [f for f in os.listdir(result_dir) if f.endswith((".jpg", ".png"))]
    
    # 读取图片并转换为base64
    # images = []
    # captions = []
    
    # for img_file in image_files[:20]:  # 最多显示20张
    #     img_path = os.path.join(result_dir, img_file)
    #     try:
    #         with open(img_path, "rb") as img_file:
    #             encoded = base64.b64encode(img_file.read()).decode()
    #             images.append(f"data:image/jpeg;base64,{encoded}")
    #             captions.append(os.path.basename(img_path))
    #     except:
    #         continue
            
    # return list(zip(images, captions))  # 返回图片和文件名对
    results = []
    for img_file in image_files[:20]:  # 最多显示20张
        img_path = os.path.join(result_dir, img_file)
        try:
            results.append((img_path, os.path.basename(img_path)))
        except Exception as e:
            print(f"加载图片失败: {img_path} | 错误: {str(e)}")
            continue
            
    return results

# 创建Gradio界面
# with gr.Blocks(css="""
#     .result-gallery {max-height: 800px; overflow-y: auto}
#     .gallery-item {width: 200px !important; height: 200px !important}
# """) as demo:
with gr.Blocks(css="""
    .result-gallery {overflow-y: visible !important}  /* 禁用垂直滚动 */
    .gallery-item {width: 500px !important; height: 500px !important}  /* 缩小缩略图尺寸 */
""") as demo:
    gr.Markdown("# ")
    
    with gr.Tabs():
        # 文本搜索标签页
        with gr.TabItem("文本搜索"):
            with gr.Row():
                text_input = gr.Textbox(
                    label="输入查询文本",
                    placeholder="如：夕阳下的海滩",
                    lines=2
                )
                text_btn = gr.Button("搜索", variant="primary")
            
            text_output = gr.Gallery(
                label="搜索结果",
                columns=5,
                height="auto",
                object_fit="contain",
                elem_classes=["result-gallery", "gallery-item"]
            )
            
            text_btn.click(
                fn=search_by_text,
                inputs=[text_input],
                outputs=text_output
            )
        
        # 图像搜索标签页
        with gr.TabItem("图像搜索"):
            with gr.Row():
                image_input = gr.Image(
                    label="上传查询图片",
                    type="pil"
                )
                image_btn = gr.Button("搜索", variant="primary")
            
            image_output = gr.Gallery(
                label="搜索结果",
                columns=5,
                height="auto",
                object_fit="contain",
                elem_classes=["result-gallery", "gallery-item"]
            )
            
            image_btn.click(
                fn=search_by_image,
                inputs=[image_input],
                outputs=image_output
            )



if __name__ == "__main__":
    demo.launch()
# ... existing code ...