# 多模态中文CLIP图像检索系统

这是一个基于中文CLIP模型和Milvus向量数据库构建的多模态图像检索系统，支持文本到图像和图像到图像的跨模态检索。

## 系统架构

```mermaid
graph LR
A[用户输入] --> B{输入类型}
B -->|文本| C[文本编码器]
B -->|图像| D[图像编码器]
C --> E[向量嵌入]
D --> E
E --> F[Milvus向量数据库]
F --> G[相似性搜索]
G --> H[结果展示]
```

## 功能特性

- 🖼️ **多模态检索**：支持文本到图像和图像到图像检索
- 🖼️ **图像分类**：支持图像自动分类，图片标签生成
- ⚡ **高效搜索**：基于Milvus向量数据库的快速相似性搜索
- 🧠 **中文优化**：使用中文CLIP模型，针对中文语境优化
- 📊 **可视化展示**：直观的搜索结果网格展示
- 🔧 **可扩展架构**：模块化设计，易于扩展新功能

## 环境准备

### 1. 安装Milvus向量数据库

```bash
# 下载Milvus Docker Compose文件
wget https://github.com/milvus-io/milvus/releases/download/v2.5.10/milvus-standalone-docker-compose.yml -O docker-compose.yml

# 启动Milvus服务
docker compose up -d
```

### 2. 安装Python依赖

```bash
# 安装PyTorch
pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124

# 安装中文CLIP
pip install cn_clip

# 安装Milvus客户端
pip install pymilvus

# 安装其他依赖
pip install pillow tqdm ipython

# 安装FastAPI和Uvicorn
pip install fastapi uvicorn

# 安装文件上传支持
pip install python-multipart

# 交互web UI界面，gradio
pip install gradio requests pillow

```

### 3. 验证安装

```bash
# 检查Milvus状态
docker ps

# 检查PyMilvus版本
pip show pymilvus
```

## 快速开始

### 项目结构

```
multimodal_search/
├── clip_encoder.py                # 中文CLIP编码器
├── multimodal_vector_db_manager.py # 向量数据库管理
├── multimodal_retrieval_system.py # 多模态检索系统
├── retrieval_api.py                # API接口
├── retrieval_ui.py                # 用户界面
├── test_api.py                    # 测试API
├── readme.md                   # 说明
└── requirements.txt               # 依赖列表
```

### 使用步骤

1. **初始化编码器和数据库**

```python
from clip_encoder import ChineseClipEncoder
from multimodal_vector_db_manager import MilvusMultimodalManager, ImageEmbeddingInserter

# 初始化CLIP编码器
clip_encoder = ChineseClipEncoder(model_name="ViT-B-16")

# 初始化Milvus管理器
milvus_manager = MilvusMultimodalManager(
    uri="http://localhost:19530",
    collection_name="multimodal_chinese_clip"
)

# 设置集合（创建、索引、加载）
if milvus_manager.setup_collection(recreate=True):
    print("集合设置成功")
```

2. **插入图像数据**

```python
# 初始化插入器
inserter = ImageEmbeddingInserter(milvus_manager, clip_encoder)

# 处理图像并插入数据库
stats = inserter.process_images_and_insert(
    input_dir_path="image_dataset",
    ext_list=['*.jpg', '*.jpeg', '*.png'],
    batch_size=500
)

print(f"插入完成！总记录数: {stats.get('row_count', 'N/A')}")
```

3. **执行检索**

```python
from multimodal_retrieval_system import MultimodalRetrievalSystem

# 初始化检索系统
searcher = MultimodalRetrievalSystem(
    milvus_manager.milvus_client,
    milvus_manager.collection_name,
    clip_encoder
)

# 以文搜图
query_text = "夕阳下的海滩"
searcher.visualize_search_results(query_text=query_text, grid_size=(2, 2))

# 以图搜图
query_image = "query_image.jpg"
searcher.visualize_search_results(query_image_path=query_image, grid_size=(2, 2))
```

## 核心模块说明

### 1. `clip_encoder.py`

中文CLIP模型封装，提供文本和图像的向量编码功能：

```python
encoder = ChineseClipEncoder(model_name="ViT-B-16")

# 文本向量化
text_vector = encoder.encode_text("一只可爱的猫咪")

# 图像向量化
image_vector = encoder.encode_image("cat.jpg")
```

### 2. `multimodal_vector_db_manager.py`

Milvus向量数据库管理：

- `MilvusMultimodalManager`: 管理集合生命周期（创建/删除/索引/加载）
- `ImageEmbeddingInserter`: 批量处理图像并插入向量数据库

### 3. `multimodal_retrieval_system.py`

多模态检索系统：

- `MultimodalRetrievalSystem`: 提供文本到图像和图像到图像检索功能
- 支持结果可视化展示

### 4. `retrieval_api.py`

运行服务接口api：
- 运行api服务：`python retrieval_api.py`: 
- 或使用uvicorn直接启动
`uvicorn retrieval_api:app --host 0.0.0.0 --port 8900 `

### 5. `retrieval_ui.py`

运行UI界面：
- 运行：`python retrieval_ui.py`: 
- 启动后访问 http://localhost:7860 打开交互式界面

## 高级用法

### 自定义检索参数

```python
# 文本检索
_, results = searcher.text_to_image_search(
    query_text="城市夜景",
    limit=8  # 返回8个结果
)

# 图像检索
_, results = searcher.image_to_image_search(
    image_path="night_city.jpg",
    limit=6  # 返回6个结果
)

# 自定义网格展示
searcher.visualize_search_results(
    results=results,
    grid_size=(3, 2),  # 3行2列
    image_size=(300, 300)  # 图像大小
)
```
**向量存储位置说明：**
```
./volumes/
├── minio/              # 向量数据主体 (约占总大小90%)
│   └── milvus-bucket/ 
│       └── files/
│           └── multimodal_chinese_clip/  # 您的集合
│               ├── [分区ID]/
│               │   └── [段ID]/
│               │       ├── 4365697668/   # 向量数据文件
│               │       └── 4365697670/   # 索引文件
├── etcd/               # 元数据 (约5%)
│   └── member/
│       ├── snap/       # 元数据快照
│       └── wal/        # 写前日志
└── milvus/             # 服务数据 (约5%)
    ├── logs/           # 日志文件
    ├── conf/           # 配置文件
    └── data/           # 临时数据
```
```bash
#查看MinIO存储大小（包含向量和索引）
du -sh ./volumes/minio

# 查看etcd元数据大小
du -sh ./volumes/etcd

# 查看Milvus服务数据大小
du -sh ./volumes/milvus

# 查看整个存储目录大小
du -sh ./volumes
```


### 性能优化建议

1. **批量处理**：插入数据时使用较大的batch_size（如500-1000）
2. **GPU加速**：确保使用支持CUDA的GPU
3. **索引优化**：根据数据量调整索引参数
4. **图像分类准确率提高**：换模型或者其他优化
5. **增删查改的功能**：milvus增删查改功能
6. **模型优化**：OpenVINO、onnx，tensorRT进行加速模型推理
7. **向量数据库迁移**：采用其他的向量数据库Weaviate威维特 进行迁移

## 常见问题

### Q1: 集合加载超时 "集合加载超时" 或 "load_collection返回None"

**问题描述**：
- 运行时显示"正在加载集合 multimodal_chinese_clip"
- 然后显示"集合加载超时"
- 或者疑惑为什么`load_collection()`返回`None`

**根本原因**：
1. **`load_collection()`返回`None`是正常的** - 这是PyMilvus的设计，该方法只发起加载请求
2. **状态判断错误** - PyMilvus 2.4.x版本中`get_load_state()`返回`LoadState`枚举对象，不是字符串
3. **代码中使用`state == 'Loaded'`判断失败** - 实际状态是`<LoadState: Loaded>`

**解决方案**：
```python
# 错误的判断方式
if state == 'Loaded':
    return True

# 正确的判断方式（已修复）
if 'Loaded' in str(state) or state == 'Loaded':
    return True
```

**验证修复**：
```bash
# 激活环境
source activate search-milvus

# 运行诊断脚本
python diagnose_milvus.py
```

### Q2: 运行时报错"无法连接Milvus服务"

**解决方案**：
1. 检查Milvus容器是否正常运行：`docker ps`
2. 验证Milvus服务端口(19530)是否可访问
3. 确保使用正确的URI：`http://localhost:19530`

### Q3: 中文CLIP模型下载失败

**解决方案**：
1. 手动下载模型到`./chinese_clip_model`目录
2. 设置代理：`export HTTP_PROXY=http://your.proxy:port`
3. 使用国内镜像源

### Q4: 检索结果不相关

**解决方案**：
1. 尝试不同的查询表达方式
2. 确保数据集覆盖查询主题
3. 调整CLIP模型版本（如使用更大的模型）

### Q5: 集合中没有数据 "row_count: 0"

**问题描述**：
集合创建和加载成功，但统计信息显示`row_count: 0`

**解决方案**：
1. 确保已运行图像插入流程：
```python
# 取消注释并运行插入代码
inserter = ImageEmbeddingInserter(milvus_manager, clip_encoder)
stats = inserter.process_images_and_insert(
    input_dir_path="your_image_directory",
    ext_list=['*.jpg', '*.jpeg', '*.png'],
    batch_size=500
)
```

2. 检查图像目录路径是否正确
3. 确保图像目录中有支持的图像文件

## 故障排除工具

项目提供了诊断脚本来帮助排查问题：

### 1. 集合诊断脚本

```bash
# 激活环境
source activate search-milvus

# 运行诊断
python diagnose_milvus.py
```

**诊断内容**：
- ✓ Milvus服务连接状态
- ✓ 集合存在性检查
- ✓ 集合索引状态
- ✓ 当前加载状态
- ✓ 集合统计信息

### 2. 快速修复测试

```bash
# 测试修复后的加载功能
python test_load_fix.py
```

### 3. 手动验证步骤

如果自动诊断无法解决问题，可以手动验证：

```python
from pymilvus import MilvusClient

# 1. 测试连接
client = MilvusClient(uri="http://localhost:19530")
print("集合列表:", client.list_collections())

# 2. 检查特定集合
collection_name = "multimodal_chinese_clip"
if client.has_collection(collection_name):
    # 检查加载状态
    state = client.get_load_state(collection_name)
    print(f"加载状态: {state}")

    # 检查统计信息
    stats = client.get_collection_stats(collection_name)
    print(f"统计信息: {stats}")
else:
    print("集合不存在，需要重新创建")
```

## 贡献指南

欢迎贡献！请遵循以下步骤：

1. Fork本项目
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送到分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

本项目采用 [MIT 许可证](LICENSE)

## 致谢

- [Chinese-CLIP](https://github.com/OFA-Sys/Chinese-CLIP) - 开源中文多模态模型
- [Milvus](https://milvus.io/) - 开源向量数据库
- [PyTorch](https://pytorch.org/) - 深度学习框架

---

**开始构建您的多模态图像检索系统吧！** 🚀

