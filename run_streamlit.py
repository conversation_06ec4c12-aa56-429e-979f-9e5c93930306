#!/usr/bin/env python3
"""
Streamlit界面启动脚本
解决中文显示和弃用警告问题
"""

import os
import sys
import subprocess
import warnings

def setup_environment():
    """设置环境变量和配置"""
    # 设置中文环境
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    
    # 设置Streamlit配置
    os.environ['STREAMLIT_SERVER_PORT'] = '8501'
    os.environ['STREAMLIT_SERVER_ADDRESS'] = 'localhost'
    os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
    
    # 忽略警告
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", message=".*use_column_width.*")

def create_streamlit_config():
    """创建Streamlit配置文件"""
    config_dir = os.path.expanduser("~/.streamlit")
    os.makedirs(config_dir, exist_ok=True)
    
    config_content = """
[global]
developmentMode = false
showWarningOnDirectExecution = false

[server]
port = 8501
address = "localhost"
headless = false
runOnSave = true
allowRunOnSave = true

[browser]
gatherUsageStats = false
serverAddress = "localhost"
serverPort = 8501

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
font = "sans serif"

[logger]
level = "error"
messageFormat = "%(asctime)s %(message)s"
"""
    
    config_path = os.path.join(config_dir, "config.toml")
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ Streamlit配置文件已创建: {config_path}")

def check_dependencies():
    """检查必要依赖"""
    required_packages = ['streamlit', 'requests', 'PIL', 'numpy']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ 缺少依赖包: {', '.join(missing)}")
        print("请运行: pip install streamlit requests pillow numpy")
        return False
    
    return True

def start_streamlit():
    """启动Streamlit应用"""
    print("🚀 启动Streamlit多模态搜索界面...")
    
    # 设置环境
    setup_environment()
    
    # 创建配置文件
    create_streamlit_config()
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 启动命令
    cmd = [
        sys.executable, '-m', 'streamlit', 'run',
        'streamlit_search_ui.py',
        '--server.port', '8501',
        '--server.address', 'localhost',
        '--server.headless', 'false',
        '--browser.gatherUsageStats', 'false',
        '--global.developmentMode', 'false',
        '--global.showWarningOnDirectExecution', 'false'
    ]
    
    try:
        print("🌐 界面地址: http://localhost:8501")
        print("📝 支持文本搜索和图像上传搜索")
        print("⚠️  请确保API服务已启动 (python retrieval_api.py)")
        print("\n按 Ctrl+C 停止服务")
        
        # 启动Streamlit
        subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
        
    except KeyboardInterrupt:
        print("\n👋 Streamlit服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_streamlit()
