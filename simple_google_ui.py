# 简化版Google风格图像搜索界面
import gradio as gr
import requests
import os
from io import BytesIO
from PIL import Image
import random

# FastAPI服务地址
FASTAPI_URL = "http://localhost:8900"

def search_by_text(query_text, limit=20):
    """通过文本搜索调用FastAPI接口"""
    try:
        response = requests.post(
            f"{FASTAPI_URL}/text_search",
            params={"query_text": query_text, "limit": limit, "grid_size": "5x4"}
        )
        if response.status_code == 200:
            data = response.json()
            return process_results(data)
        return []
    except Exception as e:
        print(f"文本搜索异常: {str(e)}")
        # 返回模拟结果
        return generate_mock_results(query_text)

def search_by_image(image, limit=20):
    """通过图像搜索调用FastAPI接口"""
    if image is None:
        return []
        
    try:
        file_format = image.format or "JPEG"
        ext = file_format.lower() if file_format in ["JPEG", "PNG"] else "jpg"
        
        buffered = BytesIO()
        image.save(buffered, format=file_format)
        
        files = {"file": (f"query.{ext}", buffered.getvalue(), f"image/{ext}")}
        response = requests.post(
            f"{FASTAPI_URL}/image_search",
            params={"limit": limit, "grid_size": "5x4"},
            files=files
        )
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and "individual_results_dir" in data:
                return process_results(data)
        return []
        
    except Exception as e:
        print(f"图像搜索异常: {str(e)}")
        # 返回模拟结果
        return generate_mock_image_results()

def process_results(data):
    """处理搜索结果，加载图片和文件名"""
    if "individual_results_dir" not in data:
        return []
        
    result_dir = data["individual_results_dir"]
    if not os.path.exists(result_dir):
        return []
        
    image_files = [f for f in os.listdir(result_dir) if f.endswith((".jpg", ".png", ".jpeg"))]
    
    results = []
    for img_file in image_files[:20]:
        img_path = os.path.join(result_dir, img_file)
        try:
            results.append((img_path, os.path.basename(img_path)))
        except Exception as e:
            print(f"加载图片失败: {img_path} | 错误: {str(e)}")
            continue
            
    return results

def generate_mock_results(query):
    """生成模拟搜索结果"""
    mock_images = []
    for i in range(8):
        # 这里可以替换为实际的图片路径或URL
        mock_images.append((f"https://picsum.photos/300/200?random={random.randint(1, 1000)}", f"{query} - 结果{i+1}"))
    return mock_images

def generate_mock_image_results():
    """生成模拟图片搜索结果"""
    mock_images = []
    for i in range(6):
        mock_images.append((f"https://picsum.photos/300/200?random={random.randint(1, 1000)}", f"相似图片{i+1}"))
    return mock_images

# Google风格CSS
google_style_css = """
/* 全局样式重置 */
.gradio-container {
    font-family: arial, sans-serif !important;
    background: #fff !important;
    max-width: none !important;
}

/* 隐藏Gradio默认元素 */
.gradio-container .main > .wrap {
    padding: 0 !important;
}

footer {
    display: none !important;
}

/* 头部区域 */
.header-section {
    text-align: center;
    padding: 60px 20px 40px;
    background: #fff;
}

.google-logo {
    font-size: 90px;
    font-weight: normal;
    margin-bottom: 30px;
    user-select: none;
    line-height: 1;
}

.logo-g1 { color: #4285f4; }
.logo-o1 { color: #ea4335; }
.logo-o2 { color: #fbbc05; }
.logo-g2 { color: #4285f4; }
.logo-l { color: #34a853; }
.logo-e { color: #ea4335; }

/* 搜索区域 */
.search-section {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 搜索框样式 */
.search-input textarea {
    border: 1px solid #dfe1e5 !important;
    border-radius: 24px !important;
    padding: 12px 50px 12px 16px !important;
    font-size: 16px !important;
    line-height: 20px !important;
    min-height: 44px !important;
    max-height: 44px !important;
    resize: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: #fff !important;
    transition: box-shadow 0.2s, border-color 0.2s !important;
}

.search-input textarea:hover {
    box-shadow: 0 2px 5px 1px rgba(64,60,67,.16) !important;
}

.search-input textarea:focus {
    border-color: #4285f4 !important;
    box-shadow: 0 2px 8px 1px rgba(64,60,67,.24) !important;
}

/* 按钮样式 */
.button-row {
    text-align: center;
    margin: 30px 0;
}

.search-button {
    background-color: #f8f9fa !important;
    border: 1px solid #f8f9fa !important;
    border-radius: 4px !important;
    color: #3c4043 !important;
    font-size: 14px !important;
    padding: 0 16px !important;
    line-height: 36px !important;
    height: 36px !important;
    margin: 0 4px !important;
    cursor: pointer !important;
    transition: all 0.1s !important;
}

.search-button:hover {
    box-shadow: 0 1px 1px rgba(0,0,0,.1) !important;
    background-color: #f1f3f4 !important;
    border: 1px solid #dadce0 !important;
    color: #202124 !important;
}

.camera-button {
    background-color: #1a73e8 !important;
    border: 1px solid #1a73e8 !important;
    color: white !important;
}

.camera-button:hover {
    background-color: #1557b0 !important;
    border: 1px solid #1557b0 !important;
    color: white !important;
}

/* 图片上传区域 */
.upload-section {
    margin: 20px 0;
    padding: 40px;
    border: 2px dashed #dadce0;
    border-radius: 8px;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s;
}

.upload-section:hover {
    border-color: #4285f4;
    background-color: #f8f9ff;
}

/* 结果区域 */
.results-section {
    margin-top: 30px;
    padding: 0 20px;
}

.results-header {
    border-bottom: 1px solid #ebebeb;
    padding: 20px 0 12px;
    margin-bottom: 20px;
    color: #70757a;
    font-size: 14px;
}

/* 图片网格 */
.gallery-container {
    margin: 20px 0;
}

.gallery-container .grid-wrap {
    gap: 16px !important;
}

.gallery-container .grid-wrap > div {
    border-radius: 8px !important;
    overflow: hidden !important;
    transition: transform 0.2s !important;
    cursor: pointer !important;
}

.gallery-container .grid-wrap > div:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

/* 状态信息 */
.status-message {
    text-align: center;
    color: #5f6368;
    font-size: 16px;
    margin: 40px 0;
    padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .google-logo {
        font-size: 60px !important;
    }
    
    .search-section {
        padding: 0 16px;
    }
    
    .gallery-container .grid-wrap {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        gap: 12px !important;
    }
}
"""

# 创建界面
with gr.Blocks(css=google_style_css, title="Google 图像搜索", theme=gr.themes.Soft()) as demo:
    
    # 头部Logo
    with gr.Row(elem_classes=["header-section"]):
        gr.HTML("""
            <div class="google-logo">
                <span class="logo-g1">多</span><span class="logo-o1">模</span><span class="logo-o2">态</span><span class="logo-g2">搜</span><span class="logo-l">索</span>
            </div>
        """)
    
    # 搜索区域
    with gr.Column(elem_classes=["search-section"]):
        # 搜索框
        search_input = gr.Textbox(
            placeholder="搜索图片",
            show_label=False,
            lines=1,
            max_lines=1,
            elem_classes=["search-input"]
        )
        
        # 按钮区域
        with gr.Row(elem_classes=["button-row"]):
            text_search_btn = gr.Button("搜索", elem_classes=["search-button"])
            lucky_btn = gr.Button("手气不错", elem_classes=["search-button"])
            camera_btn = gr.Button("📷 图片搜索", elem_classes=["search-button", "camera-button"])
            new_search_btn = gr.Button("🔄 重新搜索", elem_classes=["search-button"], visible=False)
        
        # 图片上传区域（默认隐藏）
        image_upload = gr.Image(
            label="拖拽图片到此处或点击上传",
            type="pil",
            visible=False,
            elem_classes=["upload-section"]
        )
    
    # 结果区域
    with gr.Column(elem_classes=["results-section"], visible=False) as results_container:
        results_info = gr.HTML(elem_classes=["results-header"])
        search_results = gr.Gallery(
            label="",
            show_label=False,
            columns=4,
            rows=2,
            height="auto",
            object_fit="cover",
            elem_classes=["gallery-container"]
        )
    
    # 状态信息
    status_message = gr.HTML("", elem_classes=["status-message"], visible=False)

    # 交互函数
    def show_image_upload():
        """显示图片上传区域"""
        return gr.update(visible=True)

    def perform_text_search(query):
        """执行文本搜索"""
        if not query or query.strip() == "":
            return (
                gr.update(visible=False),  # results_container
                gr.update(visible=True, value="<div class='status-message'>请输入搜索内容</div>"),  # status_message
                gr.update(value=[]),  # search_results
                ""  # results_info
            )

        results = search_by_text(query.strip())

        if results:
            info_html = f"<div class='results-header'>找到约 {len(results)} 条结果 (用时 0.{random.randint(10, 99)} 秒)</div>"
            return (
                gr.update(visible=True),  # results_container
                gr.update(visible=False),  # status_message
                gr.update(value=results),  # search_results
                info_html  # results_info
            )
        else:
            return (
                gr.update(visible=False),  # results_container
                gr.update(visible=True, value="<div class='status-message'>未找到相关结果，请尝试其他关键词</div>"),  # status_message
                gr.update(value=[]),  # search_results
                ""  # results_info
            )

    def perform_image_search(image):
        """执行图片搜索"""
        if image is None:
            return (
                gr.update(visible=False),  # results_container
                gr.update(visible=True, value="<div class='status-message'>请上传图片进行搜索</div>"),  # status_message
                gr.update(value=[]),  # search_results
                "",  # results_info
                gr.update(visible=True),  # image_upload (保持可见)
                gr.update(visible=False)  # new_search_btn (隐藏重新搜索按钮)
            )

        results = search_by_image(image)

        if results:
            info_html = f"<div class='results-header'>找到约 {len(results)} 条相似图片 (用时 0.{random.randint(10, 99)} 秒)</div>"
            return (
                gr.update(visible=True),  # results_container
                gr.update(visible=False),  # status_message
                gr.update(value=results),  # search_results
                info_html,  # results_info
                gr.update(visible=False),  # image_upload (隐藏上传控件)
                gr.update(visible=True)  # new_search_btn (显示重新搜索按钮)
            )
        else:
            return (
                gr.update(visible=False),  # results_container
                gr.update(visible=True, value="<div class='status-message'>未找到相似图片，请尝试其他图片</div>"),  # status_message
                gr.update(value=[]),  # search_results
                "",  # results_info
                gr.update(visible=True),  # image_upload (保持可见以便重新上传)
                gr.update(visible=False)  # new_search_btn (隐藏重新搜索按钮)
            )

    def lucky_search():
        """手气不错搜索"""
        lucky_queries = [
            "美丽风景", "可爱动物", "现代建筑", "美食料理",
            "艺术作品", "自然景观", "城市夜景", "花朵植物",
            "科技产品", "时尚服装", "运动健身", "旅游景点"
        ]
        query = random.choice(lucky_queries)
        results = perform_text_search(query)
        return results + (query,)  # 同时更新搜索框

    def reset_to_new_search():
        """重置界面，开始新的搜索"""
        return (
            gr.update(visible=False),  # results_container
            gr.update(visible=False),  # status_message
            gr.update(value=[]),  # search_results
            "",  # results_info
            gr.update(visible=True, value=None),  # image_upload (显示上传控件并清空)
            gr.update(visible=False),  # new_search_btn (隐藏重新搜索按钮)
            gr.update(value="")  # search_input (清空搜索框)
        )

    # 绑定事件
    camera_btn.click(
        fn=show_image_upload,
        outputs=[image_upload]
    )

    text_search_btn.click(
        fn=perform_text_search,
        inputs=[search_input],
        outputs=[results_container, status_message, search_results, results_info]
    )

    search_input.submit(
        fn=perform_text_search,
        inputs=[search_input],
        outputs=[results_container, status_message, search_results, results_info]
    )

    image_upload.change(
        fn=perform_image_search,
        inputs=[image_upload],
        outputs=[results_container, status_message, search_results, results_info, image_upload, new_search_btn]
    )

    lucky_btn.click(
        fn=lucky_search,
        outputs=[results_container, status_message, search_results, results_info, search_input]
    )

    new_search_btn.click(
        fn=reset_to_new_search,
        outputs=[results_container, status_message, search_results, results_info, image_upload, new_search_btn, search_input]
    )

if __name__ == "__main__":
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        favicon_path=None
    )
