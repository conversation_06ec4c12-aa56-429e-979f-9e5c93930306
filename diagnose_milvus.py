#!/usr/bin/env python3
"""
Milvus集合诊断脚本
用于排查集合加载超时问题
"""

from multimodal_vector_db_manager import MilvusMultimodalManager
import time

def main():
    print("=== Milvus集合诊断工具 ===\n")
    
    # 初始化管理器
    try:
        milvus_manager = MilvusMultimodalManager(
            uri="http://localhost:19530",
            collection_name="multimodal_chinese_clip"
        )
        print("✓ Milvus管理器初始化成功")
    except Exception as e:
        print(f"✗ Milvus管理器初始化失败: {e}")
        return
    
    # 运行诊断
    print("\n1. 运行集合诊断...")
    diagnosis_result = milvus_manager.diagnose_collection()
    
    if not diagnosis_result:
        print("诊断发现问题，请检查上述错误信息")
        return
    
    # 尝试手动加载
    print("\n2. 尝试手动加载集合...")
    try:
        # 检查当前状态
        current_state = milvus_manager.milvus_client.get_load_state(
            collection_name=milvus_manager.collection_name
        )
        print(f"加载前状态: {current_state}")
        
        # 如果已经加载，先释放
        if current_state.get('state') == 'Loaded':
            print("集合已加载，先释放...")
            milvus_manager.milvus_client.release_collection(
                collection_name=milvus_manager.collection_name
            )
            time.sleep(2)
            
        # 尝试加载
        print("开始加载集合...")
        load_result = milvus_manager.load_collection(timeout=120)  # 增加到2分钟
        
        if load_result:
            print("✓ 集合加载成功！")
        else:
            print("✗ 集合加载失败")
            
    except Exception as e:
        print(f"✗ 手动加载过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 最终状态检查
    print("\n3. 最终状态检查...")
    try:
        final_state = milvus_manager.milvus_client.get_load_state(
            collection_name=milvus_manager.collection_name
        )
        print(f"最终状态: {final_state}")
        
        if final_state.get('state') == 'Loaded':
            print("✓ 集合现在已成功加载到内存")
        else:
            print("✗ 集合仍未加载")
            
    except Exception as e:
        print(f"✗ 检查最终状态失败: {e}")

def check_milvus_service():
    """检查Milvus服务状态"""
    print("\n=== 检查Milvus服务状态 ===")
    
    try:
        from pymilvus import MilvusClient
        client = MilvusClient(uri="http://localhost:19530")
        
        # 测试基本连接
        collections = client.list_collections()
        print(f"✓ Milvus服务正常，发现 {len(collections)} 个集合")
        
        # 检查服务器信息
        try:
            # 注意：某些版本可能没有这些方法
            print("尝试获取服务器信息...")
        except Exception as e:
            print(f"获取服务器信息失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ Milvus服务连接失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查Milvus服务是否启动: docker ps")
        print("2. 检查端口是否正确: 默认19530")
        print("3. 检查防火墙设置")
        print("4. 重启Milvus服务: docker compose restart")
        return False

if __name__ == "__main__":
    # 首先检查Milvus服务
    if not check_milvus_service():
        exit(1)
    
    # 运行主诊断
    main()
    
    print("\n=== 诊断完成 ===")
    print("\n如果问题仍然存在，请检查:")
    print("1. Milvus服务器日志: docker compose logs milvus-standalone")
    print("2. 系统资源使用情况: htop 或 top")
    print("3. 磁盘空间: df -h")
    print("4. 内存使用: free -h")
