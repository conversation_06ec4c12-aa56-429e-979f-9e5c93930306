#!/usr/bin/env python3
"""
测试数据插入和统计功能
"""

from multimodal_vector_db_manager import MilvusMultimodalManager
import numpy as np
import time

def test_manual_insert():
    """手动测试数据插入"""
    print("=== 测试手动数据插入 ===\n")
    
    # 初始化管理器
    milvus_manager = MilvusMultimodalManager(
        uri="http://localhost:19530",
        collection_name="multimodal_chinese_clip"
    )
    
    # 检查集合状态
    print("1. 检查集合当前状态...")
    stats_before = milvus_manager.get_collection_stats()
    print(f"插入前统计: {stats_before}")
    
    # 创建测试数据
    print("\n2. 创建测试数据...")
    test_data = []
    for i in range(5):
        # 创建512维的随机向量
        vector = np.random.random(512).astype(np.float32).tolist()
        test_data.append({
            "vectors": vector,
            "filepath": f"/test/image_{i}.jpg"
        })
    
    print(f"创建了 {len(test_data)} 条测试数据")
    
    # 插入数据
    print("\n3. 插入测试数据...")
    try:
        res = milvus_manager.milvus_client.insert(
            collection_name=milvus_manager.collection_name,
            data=test_data
        )
        print(f"插入结果: {res}")
        
        # 立即检查统计（可能还是0）
        stats_immediate = milvus_manager.get_collection_stats()
        print(f"插入后立即统计: {stats_immediate}")
        
        # 刷新数据
        print("\n4. 刷新数据到磁盘...")
        milvus_manager.milvus_client.flush(
            collection_names=[milvus_manager.collection_name]
        )
        print("刷新完成")
        
        # 等待统计更新
        print("\n5. 等待统计信息更新...")
        for i in range(10):
            time.sleep(1)
            stats_after = milvus_manager.get_collection_stats()
            print(f"第{i+1}秒后统计: {stats_after}")
            
            if stats_after.get('row_count', 0) > 0:
                print(f"✓ 成功！检测到 {stats_after['row_count']} 条记录")
                break
        else:
            print("⚠ 10秒后统计信息仍未更新")
            
    except Exception as e:
        print(f"✗ 插入失败: {e}")
        import traceback
        traceback.print_exc()

def check_existing_data():
    """检查现有数据"""
    print("\n=== 检查现有数据 ===\n")
    
    milvus_manager = MilvusMultimodalManager(
        uri="http://localhost:19530",
        collection_name="multimodal_chinese_clip"
    )
    
    # 获取统计信息
    stats = milvus_manager.get_collection_stats()
    print(f"当前统计信息: {stats}")
    
    # 尝试查询一些数据
    try:
        # 查询前10条记录
        results = milvus_manager.milvus_client.query(
            collection_name=milvus_manager.collection_name,
            filter="",  # 无过滤条件
            output_fields=["id", "filepath"],
            limit=10
        )
        
        print(f"查询到 {len(results)} 条记录:")
        for i, record in enumerate(results[:5]):  # 只显示前5条
            print(f"  {i+1}. ID: {record.get('id')}, 路径: {record.get('filepath')}")
            
        if len(results) > 5:
            print(f"  ... 还有 {len(results)-5} 条记录")
            
    except Exception as e:
        print(f"查询数据时出错: {e}")

def force_refresh_stats():
    """强制刷新统计信息"""
    print("\n=== 强制刷新统计信息 ===\n")
    
    milvus_manager = MilvusMultimodalManager(
        uri="http://localhost:19530",
        collection_name="multimodal_chinese_clip"
    )
    
    try:
        # 强制刷新
        print("执行强制刷新...")
        milvus_manager.milvus_client.flush(
            collection_names=[milvus_manager.collection_name]
        )
        
        # 多次检查统计
        for i in range(5):
            time.sleep(2)
            stats = milvus_manager.get_collection_stats()
            print(f"第{(i+1)*2}秒: {stats}")
            
    except Exception as e:
        print(f"刷新时出错: {e}")

def main():
    print("数据插入问题诊断工具\n")
    
    # 1. 检查现有数据
    check_existing_data()
    
    # 2. 强制刷新统计
    force_refresh_stats()
    
    # 3. 测试手动插入
    test_manual_insert()
    
    print("\n=== 诊断完成 ===")
    print("\n如果 row_count 仍然为 0，可能的原因:")
    print("1. 数据插入时发生了错误（检查错误日志）")
    print("2. 集合配置问题（检查schema和索引）")
    print("3. Milvus服务问题（检查服务状态）")
    print("4. 权限问题（检查连接权限）")

if __name__ == "__main__":
    main()
