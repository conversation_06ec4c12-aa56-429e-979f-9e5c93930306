#!/usr/bin/env python3
"""
纯Milvus测试，不依赖CLIP模型
"""

import os
import time
from pymilvus import MilvusClient, DataType

class SimpleMilvusManager:
    def __init__(self, uri="http://localhost:19530", collection_name="multimodal_chinese_clip"):
        self.uri = uri
        self.collection_name = collection_name
        self.milvus_client = MilvusClient(uri=uri)
    
    def create_schema(self):
        schema = self.milvus_client.create_schema(
            auto_id=True,
            enable_dynamic_field=True,
            description="多模态中文CLIP向量存储"
        )
        schema.add_field(field_name="id", datatype=DataType.INT64, description='主键ID', is_primary=True)
        schema.add_field(field_name="vectors", datatype=DataType.FLOAT_VECTOR, description='嵌入向量', dim=512)
        schema.add_field(field_name="filepath", datatype=DataType.VARCHAR, description='文件路径', max_length=500)
        return schema

    def create_collection(self, timeout=3):
        try:
            schema = self.create_schema()
            self.milvus_client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                shards_num=2
            )
            print(f"开始创建集合：{self.collection_name}")
        except Exception as e:
            print(f"创建集合过程中出错: {e}")
            return False
        
        start_time = time.time()
        while True:
            if self.milvus_client.has_collection(self.collection_name):
                print(f"集合 {self.collection_name} 创建成功")
                return True
            elif time.time() - start_time > timeout:
                print(f"创建集合 {self.collection_name} 超时")
                return False
            time.sleep(0.5)

    def create_index(self):
        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            index_name="vector_index",
            field_name="vectors",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 512}
        )
        
        self.milvus_client.create_index(
            collection_name=self.collection_name,
            index_params=index_params
        )
        print(f"为集合 {self.collection_name} 创建索引成功")

    def load_collection(self, timeout=60):
        try:
            print(f"正在加载集合 {self.collection_name}")
            
            # 检查集合是否存在
            if not self.milvus_client.has_collection(self.collection_name):
                print(f"错误: 集合 {self.collection_name} 不存在")
                return False
            
            # 发起加载请求
            self.milvus_client.load_collection(collection_name=self.collection_name)
            
            # 验证加载状态
            start_time = time.time()
            while True:
                try:
                    load_state_result = self.milvus_client.get_load_state(
                        collection_name=self.collection_name
                    )
                    state = load_state_result['state']
                    print(f"当前加载状态: {state}")
                    
                    # 修复状态判断 - 处理LoadState枚举类型
                    state_str = str(state)
                    if 'Loaded' in state_str or state == 'Loaded':
                        print(f"集合 {self.collection_name} 加载完成")
                        return True
                    elif 'NotLoad' in state_str or state == 'NotLoad':
                        print("集合状态为 NotLoad，继续等待...")
                    elif 'Loading' in state_str or state == 'Loading':
                        print("集合正在加载中...")
                    else:
                        print(f"状态: {state} (类型: {type(state)})")
                        # 如果状态包含Loaded，也认为是成功的
                        if 'Loaded' in str(state):
                            print(f"集合 {self.collection_name} 加载完成")
                            return True
                        
                except Exception as e:
                    print(f"检查加载状态时出错: {e}")
                    
                if time.time() - start_time > timeout:
                    print(f"集合加载超时 (超过 {timeout} 秒)")
                    return False
                    
                time.sleep(2)
                
        except Exception as e:
            print(f"加载集合时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def setup_collection(self, recreate=False):
        if recreate:
            if self.milvus_client.has_collection(self.collection_name):
                print(f"删除现有集合 {self.collection_name}")
                self.milvus_client.drop_collection(self.collection_name)
                time.sleep(1)
        
        if not self.create_collection():
            return False
        
        self.create_index()
        
        if not self.load_collection():
            return False
        
        # 验证加载状态
        state_result = self.milvus_client.get_load_state(
            collection_name=self.collection_name
        )
        state = state_result['state']
        
        # 修复状态判断
        if 'Loaded' in str(state) or state == 'Loaded':
            print("集合加载成功，准备接收数据")
            return True
        else:
            print(f"集合加载失败，当前状态: {state} (类型: {type(state)})")
            return False

def main():
    print("=== 纯Milvus集合测试 ===\n")
    
    # 初始化管理器
    manager = SimpleMilvusManager(
        uri="http://localhost:19530",
        collection_name="test_collection"
    )
    
    # 测试完整流程
    print("测试集合设置流程...")
    result = manager.setup_collection(recreate=True)
    
    if result:
        print("\n✓ 集合设置成功！")
        
        # 检查最终状态
        stats = manager.milvus_client.get_collection_stats(manager.collection_name)
        print(f"集合统计: {stats}")
        
        load_state = manager.milvus_client.get_load_state(manager.collection_name)
        print(f"加载状态: {load_state}")
        
        print("\n🎉 测试通过！Milvus集合功能正常。")
        
    else:
        print("\n✗ 集合设置失败")

if __name__ == "__main__":
    main()
