# retrieval_ui.py
import requests
import os
import base64
from io import BytesIO
from PIL import Image

# FastAPI服务地址
FASTAPI_URL = "http://localhost:8900"

def search_by_text(query_text: str, limit: int = 4):
    """通过文本搜索调用FastAPI接口"""
    try:
        response = requests.post(
            f"{FASTAPI_URL}/text_search",
            params={"query_text": query_text, "limit": limit, "grid_size": "5x4"},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            return process_results(data)
        else:
            print(f"搜索失败: {response.json().get('detail', '未知错误')}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"网络连接错误: {str(e)}")
        return []
    except Exception as e:
        print(f"搜索异常: {str(e)}")
        return []

def search_by_image(image_file):
    """通过图像搜索调用FastAPI接口"""
    try:
        files = {"file": ("image.jpg", image_file, "image/jpeg")}
        response = requests.post(
            f"{FASTAPI_URL}/image_search",
            files=files,
            params={"limit": 4, "grid_size": "5x4"},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            return process_results(data)
        else:
            print(f"图像搜索失败: {response.json().get('detail', '未知错误')}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"网络连接错误: {str(e)}")
        return []
    except Exception as e:
        print(f"图像搜索异常: {str(e)}")
        return []

def process_results(data: dict):
    """处理搜索结果，加载图片和文件名"""
    if "individual_results_dir" not in data:
        return []
        
    result_dir = data["individual_results_dir"]
    print("result_dir:",result_dir)
    if not os.path.exists(result_dir):
        return []
        
    image_files = [f for f in os.listdir(result_dir) if f.endswith((".jpg", ".png", ".jpeg"))]
    print("image_files:", image_files)
    results = []
    for img_file in sorted(image_files)[:4]:
        img_path = os.path.join(result_dir, img_file)
        try:
            results.append((img_path, os.path.basename(img_path)))
        except Exception as e:
            print(f"加载图片失败: {img_path} | 错误: {str(e)}")
            continue
            
    return results


if __name__ == "__main__":
    # res1 = search_by_text("小桥流水人家",limit=4)
    # print("res1:",res1)
    res2 = search_by_image(open("./images_data/2007_007084.jpg","rb"))
    print("res2:",res2)
    for img_path, filename in res2:
        print(f"图片路径: {img_path}, 文件名: {filename}")
        try:
            img = Image.open(img_path)
            img.show()  # 显示图片
        except Exception as e:
            print(f"显示图片失败: {img_path} | 错误: {str(e)}")
