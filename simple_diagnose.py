#!/usr/bin/env python3
"""
简化的Milvus诊断脚本
"""

def check_milvus_connection():
    """检查Milvus连接"""
    try:
        from pymilvus import MilvusClient
        print("✓ pymilvus 模块导入成功")
        
        client = MilvusClient(uri="http://localhost:19530")
        print("✓ Milvus客户端创建成功")
        
        # 测试连接
        collections = client.list_collections()
        print(f"✓ Milvus连接成功，发现 {len(collections)} 个集合: {collections}")
        
        return client
        
    except ImportError as e:
        print(f"✗ 导入pymilvus失败: {e}")
        print("请安装: pip install pymilvus")
        return None
    except Exception as e:
        print(f"✗ Milvus连接失败: {e}")
        print("请检查:")
        print("1. Milvus服务是否启动: docker ps")
        print("2. 端口是否正确: 默认19530")
        return None

def diagnose_collection(client, collection_name="multimodal_chinese_clip"):
    """诊断特定集合"""
    print(f"\n=== 诊断集合 {collection_name} ===")
    
    # 1. 检查集合是否存在
    try:
        exists = client.has_collection(collection_name)
        if exists:
            print(f"✓ 集合 {collection_name} 存在")
        else:
            print(f"✗ 集合 {collection_name} 不存在")
            print("可用的集合:", client.list_collections())
            return False
    except Exception as e:
        print(f"✗ 检查集合存在性失败: {e}")
        return False
    
    # 2. 检查集合描述
    try:
        desc = client.describe_collection(collection_name)
        print(f"✓ 集合描述获取成功")
        print(f"  字段数量: {len(desc.get('fields', []))}")
        for field in desc.get('fields', []):
            print(f"  - {field.get('name')}: {field.get('type')}")
    except Exception as e:
        print(f"✗ 获取集合描述失败: {e}")
    
    # 3. 检查索引
    try:
        indexes = client.list_indexes(collection_name)
        if indexes:
            print(f"✓ 集合有索引: {indexes}")
        else:
            print(f"✗ 集合没有索引！这是加载失败的主要原因")
            return False
    except Exception as e:
        print(f"✗ 检查索引失败: {e}")
        return False
    
    # 4. 检查当前加载状态
    try:
        load_state = client.get_load_state(collection_name)
        print(f"✓ 当前加载状态: {load_state}")
        
        state = load_state.get('state', 'Unknown')
        if state == 'Loaded':
            print("✓ 集合已经加载到内存")
        elif state == 'NotLoad':
            print("⚠ 集合未加载")
        elif state == 'Loading':
            print("⚠ 集合正在加载中")
        else:
            print(f"⚠ 未知状态: {state}")
            
    except Exception as e:
        print(f"✗ 获取加载状态失败: {e}")
    
    # 5. 检查集合统计信息
    try:
        stats = client.get_collection_stats(collection_name)
        print(f"✓ 集合统计信息: {stats}")
        row_count = stats.get('row_count', 0)
        if row_count == 0:
            print("⚠ 集合中没有数据")
        else:
            print(f"✓ 集合包含 {row_count} 条记录")
    except Exception as e:
        print(f"✗ 获取统计信息失败: {e}")
    
    return True

def test_load_collection(client, collection_name="multimodal_chinese_clip"):
    """测试加载集合"""
    print(f"\n=== 测试加载集合 {collection_name} ===")
    
    try:
        # 检查当前状态
        current_state = client.get_load_state(collection_name)
        print(f"加载前状态: {current_state}")
        
        # 如果已经加载，先释放
        if current_state.get('state') == 'Loaded':
            print("集合已加载，先释放...")
            client.release_collection(collection_name=collection_name)
            import time
            time.sleep(3)
            print("释放完成")
        
        # 尝试加载
        print("开始加载集合...")
        client.load_collection(collection_name=collection_name)
        
        # 检查加载状态
        import time
        max_wait = 60  # 最多等待60秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            state_result = client.get_load_state(collection_name)
            state = state_result.get('state', 'Unknown')
            elapsed = time.time() - start_time
            
            print(f"[{elapsed:.1f}s] 当前状态: {state}")
            
            if state == 'Loaded':
                print(f"✓ 集合加载成功！耗时 {elapsed:.1f} 秒")
                return True
            elif state in ['NotLoad', 'Loading']:
                time.sleep(2)
                continue
            else:
                print(f"✗ 未知状态: {state}")
                break
        
        print(f"✗ 集合加载超时 (超过 {max_wait} 秒)")
        return False
        
    except Exception as e:
        print(f"✗ 加载过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== 简化Milvus诊断工具 ===\n")
    
    # 1. 检查连接
    client = check_milvus_connection()
    if not client:
        return
    
    # 2. 诊断集合
    collection_name = "multimodal_chinese_clip"
    diagnosis_ok = diagnose_collection(client, collection_name)
    
    if not diagnosis_ok:
        print(f"\n✗ 集合 {collection_name} 诊断发现问题")
        print("建议:")
        print("1. 确保集合已创建")
        print("2. 确保集合有索引")
        print("3. 检查集合中是否有数据")
        return
    
    # 3. 测试加载
    load_ok = test_load_collection(client, collection_name)
    
    if load_ok:
        print("\n✓ 集合加载测试成功！")
    else:
        print("\n✗ 集合加载测试失败")
        print("建议检查:")
        print("1. Milvus服务器日志: docker compose logs milvus-standalone")
        print("2. 系统资源: 内存、CPU、磁盘空间")
        print("3. 集合数据完整性")

if __name__ == "__main__":
    main()
