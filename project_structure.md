# 多模态图像检索系统 - 重构后项目结构

## 新项目结构设计

```
multimodal-search/
├── 📂 shared/                          # 共享组件
│   ├── clip_encoder.py                 # 🧠 中文CLIP编码器
│   ├── base_retrieval.py              # 基础检索系统抽象类
│   ├── api/                           # 🚀 API服务
│   │   ├── __init__.py
│   │   ├── base_api.py                # 基础API类
│   │   ├── models.py                  # API数据模型
│   │   └── unified_api.py             # 统一API接口
│   └── ui/                            # 💎 用户界面
│       ├── __init__.py
│       ├── streamlit_ui.py            # Streamlit界面
│       ├── gradio_ui.py               # Gradio界面
│       └── unified_ui.py              # 统一UI界面
├── 📂 search-milvus/                   # Milvus实现
│   ├── __init__.py
│   ├── milvus_manager.py              # Milvus数据库管理
│   ├── milvus_retrieval.py            # Milvus检索系统
│   ├── milvus_api.py                  # Milvus API服务
│   └── docker-compose-milvus.yml      # Milvus Docker配置
├── 📂 search-weaviate/                 # Weaviate实现
│   ├── __init__.py
│   ├── weaviate_manager.py            # Weaviate数据库管理
│   ├── weaviate_retrieval.py          # Weaviate检索系统
│   ├── weaviate_api.py                # Weaviate API服务
│   └── docker-compose-weaviate.yml    # Weaviate Docker配置
│   ├── weaviate_manager.py            # Weaviate数据库管理
│   ├── weaviate_retrieval.py          # Weaviate检索系统
│   └── weaviate_api.py                # Weaviate API服务
├── 📂 config/                          # 配置文件
│   ├── milvus_config.yml
│   ├── weaviate_config.yml
│   └── app_config.yml
├── 📂 data/                           # 数据目录
│   ├── images/
│   ├── models/
│   └── results/
├── requirements.txt
├── docker-compose.yml                 # 包含Milvus和Weaviate
└── README.md