#!/usr/bin/env python3
"""
检查集合统计信息
"""

from pymilvus import MilvusClient
import time

def check_collection_stats():
    print("=== 检查集合统计信息 ===\n")
    
    # 连接Milvus
    client = MilvusClient(uri="http://localhost:19530")
    collection_name = "multimodal_chinese_clip"
    
    # 1. 基本统计信息
    print("1. 获取基本统计信息...")
    stats = client.get_collection_stats(collection_name)
    print(f"统计信息: {stats}")
    
    # 2. 强制刷新并重新检查
    print("\n2. 强制刷新数据...")
    try:
        client.flush(collection_names=[collection_name])
        print("刷新完成")
        
        # 等待并重新检查
        for i in range(5):
            time.sleep(2)
            stats = client.get_collection_stats(collection_name)
            print(f"第{(i+1)*2}秒后: {stats}")
            
    except Exception as e:
        print(f"刷新时出错: {e}")
    
    # 3. 尝试查询数据来验证
    print("\n3. 通过查询验证数据存在...")
    try:
        # 查询前10条记录
        results = client.query(
            collection_name=collection_name,
            filter="",  # 无过滤条件
            output_fields=["id", "filepath"],
            limit=10
        )
        
        print(f"✓ 查询成功！找到 {len(results)} 条记录")
        if results:
            print("前几条记录:")
            for i, record in enumerate(results[:3]):
                print(f"  {i+1}. ID: {record.get('id')}, 路径: {record.get('filepath')}")
        
        # 尝试计算总数
        print(f"\n4. 尝试计算实际记录数...")
        total_count = 0
        limit = 1000
        offset = 0
        
        while True:
            batch = client.query(
                collection_name=collection_name,
                filter="",
                output_fields=["id"],
                limit=limit,
                offset=offset
            )
            
            if not batch:
                break
                
            total_count += len(batch)
            offset += limit
            
            print(f"已计算 {total_count} 条记录...")
            
            # 防止无限循环
            if total_count > 20000:
                print("记录数超过20000，停止计算")
                break
        
        print(f"✓ 实际记录总数: {total_count}")
        
        if total_count > 0:
            print(f"\n🎉 数据插入成功！实际有 {total_count} 条记录")
            print("统计信息可能有延迟，但数据确实存在")
        else:
            print("\n❌ 没有找到任何记录")
            
    except Exception as e:
        print(f"查询时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_collection_stats()
