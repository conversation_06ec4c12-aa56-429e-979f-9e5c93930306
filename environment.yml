name: multimodal-search
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  # Python版本
  - python=3.10

  # 深度学习框架 (CUDA版本)
  - pytorch==2.6.0
  - torchvision==0.21.0
  - torchaudio==2.6.0
  - pytorch-cuda=12.4

  # 基础科学计算
  - numpy>=1.24.0
  - scipy>=1.11.0

  # 图像处理
  - pillow>=10.0.0

  # 系统工具
  - tqdm>=4.66.0

  # pip安装的包
  - pip
  - pip:
    # 中文CLIP模型
    - cn_clip
    
    # 向量数据库
    - pymilvus>=2.5.0
    
    # Web框架和API
    - fastapi>=0.104.0
    - uvicorn[standard]>=0.24.0
    - python-multipart>=0.0.6
    
    # 用户界面框架
    - streamlit>=1.28.0
    - gradio>=4.0.0
    
    # 网络请求
    - requests>=2.31.0
    
    # 开发工具（可选）
    - pytest>=7.4.0
    - black>=23.0.0
    - flake8>=6.0.0
    - isort>=5.12.0
    
    # 系统监控
    - psutil>=5.9.0
