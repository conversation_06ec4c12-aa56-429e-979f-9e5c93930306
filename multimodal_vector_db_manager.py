import os
import time
import torch
from glob import glob
from tqdm import tqdm
from PIL import Image
from pymilvus import MilvusClient, DataType

class CollectionDeletionError(Exception):
    """删除集合失败异常"""
    pass

class MilvusMultimodalManager:
    def __init__(self, uri="http://localhost:19530", collection_name="multimodal_chinese_clip"):
        """
        初始化多模态Milvus管理器
        
        参数:
        uri: Milvus服务地址
        collection_name: 集合名称
        """
        self.uri = uri
        self.collection_name = collection_name
        self.milvus_client = MilvusClient(uri=uri)
    
    def create_schema(self):
        """
        创建集合模式
        
        返回:
        schema: 创建的模式对象
        """
        schema = self.milvus_client.create_schema(
            auto_id=True,
            enable_dynamic_field=True,
            description="多模态中文CLIP向量存储"
        )
        schema.add_field(field_name="id", datatype=DataType.INT64, description='主键ID', is_primary=True)
        schema.add_field(field_name="vectors", datatype=DataType.FLOAT_VECTOR, description='嵌入向量', dim=512)
        schema.add_field(field_name="filepath", datatype=DataType.VARCHAR, description='文件路径', max_length=500)
        return schema

    def create_collection(self, timeout=3):
        """
        创建Milvus集合
        
        参数:
        timeout: 创建超时时间(秒)
        
        返回:
        bool: 创建是否成功
        """
        try:
            schema = self.create_schema()
            self.milvus_client.create_collection(
                collection_name=self.collection_name,
                schema=schema,
                shards_num=2
            )
            print(f"开始创建集合：{self.collection_name}")
        except Exception as e:
            print(f"创建集合过程中出错: {e}")
            return False
        
        # 检查集合是否创建成功
        start_time = time.time()
        while True:
            if self.milvus_client.has_collection(self.collection_name):
                print(f"集合 {self.collection_name} 创建成功")
                return True
            elif time.time() - start_time > timeout:
                print(f"创建集合 {self.collection_name} 超时")
                return False
            time.sleep(0.5)
    
    def check_and_drop_collection(self):
        """
        检查并删除同名集合
        
        返回:
        bool: 是否成功删除或集合不存在
        """
        if self.milvus_client.has_collection(self.collection_name):
            print(f"集合 {self.collection_name} 已存在")
            try:
                self.milvus_client.drop_collection(self.collection_name)
                print(f"成功删除集合：{self.collection_name}")
                return True
            except Exception as e:
                print(f"删除集合时出错: {e}")
                return False
        print(f"集合 {self.collection_name} 不存在，无需删除")
        return True
    
    def create_index(self, index_params=None):
        """
        创建集合索引
        
        参数:
        index_params: 索引参数，默认为IVF_FLAT索引
        """
        if index_params is None:
            # 默认索引参数
            index_params = self.milvus_client.prepare_index_params()
            index_params.add_index(
                index_name="vector_index",
                field_name="vectors",
                index_type="IVF_FLAT",
                metric_type="COSINE",
                params={"nlist": 512}
            )
        
        self.milvus_client.create_index(
            collection_name=self.collection_name,
            index_params=index_params
        )
        print(f"为集合 {self.collection_name} 创建索引成功")
    
    def load_collection(self, timeout=60):  # 增加超时时间到60秒
        """
        加载集合到内存

        参数:
        timeout: 加载超时时间(秒)

        返回:
        bool: 是否加载成功
        """
        try:
            print(f"正在加载集合 {self.collection_name}")

            # 检查集合是否存在
            if not self.milvus_client.has_collection(self.collection_name):
                print(f"错误: 集合 {self.collection_name} 不存在")
                return False

            # 检查集合统计信息
            try:
                stats = self.milvus_client.get_collection_stats(self.collection_name)
                print(f"集合统计信息: {stats}")
            except Exception as e:
                print(f"获取集合统计信息失败: {e}")

            # 检查索引状态
            try:
                indexes = self.milvus_client.list_indexes(self.collection_name)
                print(f"集合索引: {indexes}")
                if not indexes:
                    print("警告: 集合没有索引，这可能导致加载失败")
            except Exception as e:
                print(f"检查索引失败: {e}")

            # 发起加载请求
            self.milvus_client.load_collection(collection_name=self.collection_name)

            # 验证加载状态
            start_time = time.time()
            while True:
                try:
                    load_state_result = self.milvus_client.get_load_state(
                        collection_name=self.collection_name
                    )
                    state = load_state_result['state']
                    print(f"当前加载状态: {state}")

                    # 修复状态判断 - 处理LoadState枚举类型
                    state_str = str(state)
                    if 'Loaded' in state_str or state == 'Loaded':
                        print(f"集合 {self.collection_name} 加载完成")
                        return True
                    elif 'NotLoad' in state_str or state == 'NotLoad':
                        print("集合状态为 NotLoad，继续等待...")
                    elif 'Loading' in state_str or state == 'Loading':
                        print("集合正在加载中...")
                    else:
                        print(f"状态: {state} (类型: {type(state)})")
                        # 如果状态包含Loaded，也认为是成功的
                        if 'Loaded' in str(state):
                            print(f"集合 {self.collection_name} 加载完成")
                            return True

                except Exception as e:
                    print(f"检查加载状态时出错: {e}")

                if time.time() - start_time > timeout:
                    print(f"集合加载超时 (超过 {timeout} 秒)")
                    # 尝试获取最终状态
                    try:
                        final_state = self.milvus_client.get_load_state(
                            collection_name=self.collection_name
                        )
                        print(f"超时时的最终状态: {final_state}")
                    except:
                        pass
                    return False

                time.sleep(2)  # 增加检查间隔到2秒

        except Exception as e:
            print(f"加载集合时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_collection_stats(self):
        """
        获取集合统计信息

        返回:
        dict: 集合统计信息
        """
        try:
            stats = self.milvus_client.get_collection_stats(
                collection_name=self.collection_name
            )
            return stats
        except Exception as e:
            print(f"获取集合统计信息失败: {e}")
            return {}

    def diagnose_collection(self):
        """
        诊断集合状态，帮助排查加载问题
        """
        print(f"\n=== 诊断集合 {self.collection_name} ===")

        # 1. 检查Milvus连接
        try:
            # 尝试列出所有集合来测试连接
            collections = self.milvus_client.list_collections()
            print(f"✓ Milvus连接正常，共有 {len(collections)} 个集合")
        except Exception as e:
            print(f"✗ Milvus连接失败: {e}")
            return False

        # 2. 检查集合是否存在
        try:
            exists = self.milvus_client.has_collection(self.collection_name)
            if exists:
                print(f"✓ 集合 {self.collection_name} 存在")
            else:
                print(f"✗ 集合 {self.collection_name} 不存在")
                return False
        except Exception as e:
            print(f"✗ 检查集合存在性失败: {e}")
            return False

        # 3. 检查集合描述
        try:
            desc = self.milvus_client.describe_collection(self.collection_name)
            print(f"✓ 集合描述: {desc}")
        except Exception as e:
            print(f"✗ 获取集合描述失败: {e}")

        # 4. 检查索引
        try:
            indexes = self.milvus_client.list_indexes(self.collection_name)
            if indexes:
                print(f"✓ 集合有索引: {indexes}")
                for index_name in indexes:
                    try:
                        index_info = self.milvus_client.describe_index(
                            collection_name=self.collection_name,
                            index_name=index_name
                        )
                        print(f"  索引 {index_name} 详情: {index_info}")
                    except Exception as e:
                        print(f"  获取索引 {index_name} 详情失败: {e}")
            else:
                print(f"✗ 集合没有索引，这会导致加载失败")
                return False
        except Exception as e:
            print(f"✗ 检查索引失败: {e}")
            return False

        # 5. 检查当前加载状态
        try:
            load_state = self.milvus_client.get_load_state(self.collection_name)
            print(f"✓ 当前加载状态: {load_state}")
        except Exception as e:
            print(f"✗ 获取加载状态失败: {e}")

        # 6. 检查集合统计信息
        try:
            stats = self.get_collection_stats()
            if stats:
                print(f"✓ 集合统计信息: {stats}")
            else:
                print("⚠ 无法获取集合统计信息")
        except Exception as e:
            print(f"✗ 获取统计信息失败: {e}")

        print("=== 诊断完成 ===\n")
        return True
    
    def setup_collection(self, recreate=False):
        """
        完整设置集合流程
        
        参数:
        recreate: 是否重新创建集合
        
        返回:
        bool: 设置是否成功
        """
        if recreate:
            if not self.check_and_drop_collection():
                raise CollectionDeletionError('删除集合失败')
        
        if not self.create_collection():
            return False
        
        self.create_index()
        
        if not self.load_collection():
            return False
        
        # 验证加载状态
        state_result = self.milvus_client.get_load_state(
            collection_name=self.collection_name
        )
        state = state_result['state']

        # 修复状态判断 - 处理LoadState枚举类型
        if 'Loaded' in str(state) or state == 'Loaded':
            print("集合加载成功，准备接收数据")
            return True
        else:
            print(f"集合加载失败，当前状态: {state} (类型: {type(state)})")
            return False


class ImageEmbeddingInserter:
    def __init__(self, milvus_manager, encoder):
        """
        初始化图像嵌入插入器
        
        参数:
        milvus_manager: Milvus集合管理器实例
        encoder: 图像编码器实例
        """
        self.milvus_manager = milvus_manager
        self.encoder = encoder
    
    def process_images_and_insert(self, input_dir_path, ext_list=['*.jpg', '*.jpeg', '*.png'], batch_size=100):
        """
        处理目录中的图像并插入到Milvus
        
        参数:
        input_dir_path: 输入目录路径
        ext_list: 支持的文件扩展名列表
        batch_size: 批量处理大小
        """
        # 获取所有图像文件路径
        image_paths = []
        for ext in ext_list:
            # 递归搜索所有匹配的文件
            pattern = os.path.join(input_dir_path, '**', ext)
            image_paths.extend(glob(pattern, recursive=True))
        
        total_images = len(image_paths)
        if total_images == 0:
            print(f"在目录 {input_dir_path} 中未找到任何图像文件")
            return
        
        print(f"总计需要处理 {total_images} 张图片")
        
        # 初始化总计时器
        total_start_time = time.time()
        processed_count = 0
        
        # 初始化进度条
        with tqdm(total=total_images, desc="处理图片并插入数据") as progress_bar:
            # 分批处理图片
            for batch_start in range(0, total_images, batch_size):
                batch_data = []
                batch_paths = image_paths[batch_start: batch_start + batch_size]
                batch_start_time = time.time()
                
                # 当前批次的向量化处理
                for image_path in batch_paths:
                    try:
                        image_embedding = self.encoder.encode_image(image_path)
                        batch_data.append({
                            "vectors": image_embedding,
                            "filepath": image_path
                        })
                    except Exception as e:
                        print(f"处理图片 {image_path} 时出错: {str(e)}")
                        continue
                
                # 批量插入当前批次到Milvus
                if batch_data:
                    try:
                        res = self.milvus_manager.milvus_client.insert(
                            collection_name=self.milvus_manager.collection_name,
                            data=batch_data
                        )
                        processed_count += len(batch_data)

                        # 打印插入结果信息
                        if res and hasattr(res, 'insert_count'):
                            print(f"批次 {batch_start//batch_size + 1}: 成功插入 {res.insert_count} 条记录")
                        elif res:
                            print(f"批次 {batch_start//batch_size + 1}: 插入完成，返回: {res}")

                        # 计算批次耗时
                        batch_duration = time.time() - batch_start_time

                        # 更新进度条
                        progress_bar.update(len(batch_data))

                        # 显示批次处理时间
                        progress_bar.set_postfix({
                            "批次耗时": f"{batch_duration:.2f}s",
                            "处理速度": f"{len(batch_data)/batch_duration:.1f}img/s"
                        })

                        # 每处理几个批次就刷新一次（可选，提高性能）
                        if (batch_start // batch_size + 1) % 10 == 0:
                            print(f"\n正在刷新数据到磁盘...")
                            try:
                                # 尝试新版本的flush方法
                                self.milvus_manager.milvus_client.flush(
                                    collection_name=self.milvus_manager.collection_name
                                )
                            except TypeError:
                                # 如果失败，尝试旧版本的flush方法
                                self.milvus_manager.milvus_client.flush(
                                    collection_names=[self.milvus_manager.collection_name]
                                )

                    except Exception as e:
                        print(f"插入批次 {batch_start} 时失败: {str(e)}")
                        import traceback
                        traceback.print_exc()
        
        # 计算总耗时
        total_duration = time.time() - total_start_time
        print(f"\n所有图片处理完成！总耗时: {total_duration:.2f}秒")
        print(f"处理图片数量: {processed_count}/{total_images}")
        print(f"平均处理速度: {processed_count/total_duration:.1f}张/秒")

        # 最终刷新：将所有数据持久化到磁盘
        if processed_count > 0:
            print(f"\n正在将 {processed_count} 条记录刷新到磁盘...")
            try:
                # 尝试新版本的flush方法（单个collection_name参数）
                try:
                    self.milvus_manager.milvus_client.flush(
                        collection_name=self.milvus_manager.collection_name
                    )
                except TypeError:
                    # 如果失败，尝试旧版本的flush方法（collection_names列表参数）
                    self.milvus_manager.milvus_client.flush(
                        collection_names=[self.milvus_manager.collection_name]
                    )
                print("✓ 数据刷新完成")

                # 等待一下让统计信息更新
                time.sleep(2)

                # 获取最新统计信息
                final_stats = self.milvus_manager.get_collection_stats()
                print(f"✓ 最终集合统计信息: {final_stats}")

                if final_stats.get('row_count', 0) > 0:
                    print(f"🎉 成功插入 {final_stats['row_count']} 条记录到集合中！")
                else:
                    print("⚠ 警告: 统计信息仍显示 row_count=0，可能需要更长时间同步")

                return final_stats

            except Exception as e:
                print(f"✗ 刷新数据时出错: {e}")
                return self.milvus_manager.get_collection_stats()
        else:
            print("⚠ 没有数据被处理")
            return self.milvus_manager.get_collection_stats()
    

if __name__ == "__main__":
    # 1. 初始化CLIP编码器
    from clip_encoder import ChineseClipEncoder
    clip_encoder = ChineseClipEncoder(model_name="ViT-B-16")

    # 2. 初始化Milvus管理器
    milvus_manager = MilvusMultimodalManager(
        uri="http://localhost:19530",
        # collection_name="multimodal_chinese_clip"
        collection_name="multimodal_chinese_clip"
    )

    # 3. 设置集合（创建、索引、加载）
    if milvus_manager.setup_collection(recreate=True):
        print("集合设置成功")
    else:
        print("集合设置失败，请检查日志")

    # 4. 初始化插入器
    inserter = ImageEmbeddingInserter(milvus_manager, clip_encoder)

    # 5. 处理图像并插入
    images_data_dir = "../../immich-app/JPEGImages"
    # images_data_dir = "../../immich-app/mx"

    if not os.path.exists(images_data_dir):
        print(f"目录 {images_data_dir} 不存在，请检查路径")
        exit(1)
    stats = inserter.process_images_and_insert(
        input_dir_path=images_data_dir ,
        ext_list=['*.jpg', '*.jpeg', '*.png'],
        batch_size=5000
    )

    # # 6. 查看集合统计信息
    print("集合统计信息:", stats)
    # 计需要处理 17148 张图片
# 处理图片并插入数据: 100%|███████████████████████████████████████████████████████████████████████████████████████████| 17148/17148 [08:24<00:00, 34.02it/s, 批次耗时=1.82s, 处理速度=26.4img/s]