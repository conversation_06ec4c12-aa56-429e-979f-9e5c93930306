"""
增强版多模态检索系统 - 支持结果保存功能
功能：
1. 文本到图像检索（以文搜图）
2. 图像到图像检索（以图搜图）
3. 结果可视化展示
4. 查询结果保存到目录
"""
from PIL import Image
from IPython.display import display
import os
import uuid
import time
import re

class MultimodalRetrievalSystem:
    def __init__(self, milvus_client, collection_name, encoder, results_dir="search_results"):
        """
        初始化多模态检索系统
        
        参数:
        milvus_client: Milvus客户端实例
        collection_name: 集合名称
        encoder: 嵌入编码器实例
        results_dir: 结果保存目录
        """
        self.milvus_client = milvus_client
        self.collection_name = collection_name
        self.encoder = encoder
        self.results_dir = results_dir
        
        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)
    
    def vector_search(self, vector, field_name="vectors", limit=10, output_fields=["filepath"]):
        """执行向量相似性搜索"""
        print("执行向量相似性搜索******************************")
        # print("output_fields:",output_fields)
        # print(self.milvus_client.search(
        #     collection_name=self.collection_name,
        #     data=[vector],
        #     anns_field=field_name,
        #     limit=limit,
        #     output_fields=output_fields))
        return self.milvus_client.search(
            collection_name=self.collection_name,
            data=[vector],
            anns_field=field_name,
            limit=limit,
            output_fields=output_fields
        )
    
    def text_to_image_search(self, query_text, limit=10):
        """文本到图像检索（以文搜图）"""
        query_embedding = self.encoder.encode_text(query_text)
        # print("query_text:",query_text)
        results = self.vector_search(
            vector=query_embedding,
            limit=limit
        )
        return query_embedding, results
    
    def image_to_image_search(self, image_path, limit=10):
        """图像到图像检索（以图搜图）"""
        query_embedding = self.encoder.encode_image(image_path)
        results = self.vector_search(
            vector=query_embedding,
            limit=limit
        )
        return query_embedding, results
    
    def display_query_image(self, image_path, image_size=(300, 300)):
        """显示查询图像"""
        img = Image.open(image_path)
        img.thumbnail(image_size)
        display(img)
        return img
    
    def create_results_grid(self, search_results, images_per_row=2, images_per_column=2, image_size=(400, 400)):
        """创建搜索结果网格图像"""
        width = image_size[0] * images_per_row
        height = image_size[1] * images_per_column
        concatenated_image = Image.new("RGB", (width, height))
        
        result_images = []
        result_paths = []  # 存储结果图像路径
        
        for result in search_results:
            for hit in result:
                try:
                    img_path = hit["entity"]["filepath"]
                    new_img_name = img_path.split('/')[-1]
                    new_path_dir = "../../immich-app/JPEGImages"
                    new_img_path = os.path.join(new_path_dir,new_img_name)
                    img = Image.open(new_img_path)
                    img = img.resize(image_size)
                    result_images.append(img)
                    result_paths.append(new_img_path)
                except Exception as e:
                    print(f"无法加载图像: {hit['entity']['filepath']} | 错误: {str(e)}")
        
        # 将图像排列到网格中
        for idx, img in enumerate(result_images):
            if idx >= images_per_row * images_per_column:
                break
            x = idx % images_per_row
            y = idx // images_per_row
            concatenated_image.paste(img, (x * image_size[0], y * image_size[1]))
        
        return concatenated_image, result_paths
    
    def _generate_filename(self, prefix, extension="jpg"):
        """生成唯一的文件名"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}_{timestamp}_{unique_id}.{extension}"
    
    def _sanitize_filename(self, text):
        """清理文本中的特殊字符"""
        return re.sub(r'[^\w\u4e00-\u9fff]', '_', text)[:50]
    
    def save_search_results(self, results_grid, result_paths, query_text=None, 
                          query_image_path=None, save_dir=None, prefix=""):
        """
        保存搜索结果到目录
        
        参数:
        results_grid: 结果网格图像
        result_paths: 结果图像路径列表
        query_text: 查询文本
        query_image_path: 查询图像路径
        save_dir: 保存目录
        prefix: 文件名前缀
        """
        # 确定保存目录
        save_dir = save_dir or self.results_dir
        
        # 创建子目录
        if query_text:
            dir_name = os.path.join(save_dir, "text_queries")
            prefix = self._sanitize_filename(query_text) if not prefix else prefix
        elif query_image_path:
            dir_name = os.path.join(save_dir, "image_queries")
            prefix = os.path.splitext(os.path.basename(query_image_path))[0] if not prefix else prefix
        else:
            dir_name = os.path.join(save_dir, "other_queries")
        
        os.makedirs(dir_name, exist_ok=True)
        
        # 保存结果网格
        grid_filename = self._generate_filename(f"{prefix}_results_grid")
        grid_path = os.path.join(dir_name, grid_filename)
        results_grid.save(grid_path)
        print(f"结果网格已保存: {grid_path}")
        
        # 保存查询图像（如果有）
        if query_image_path:
            try:
                query_img = Image.open(query_image_path)
                query_filename = self._generate_filename(f"{prefix}_query")
                query_path = os.path.join(dir_name, query_filename)
                query_img.save(query_path)
                print(f"查询图像已保存: {query_path}")
            except Exception as e:
                print(f"保存查询图像失败: {str(e)}")
        
        # 保存结果图像
        result_dir = os.path.join(dir_name, "individual_results")
        os.makedirs(result_dir, exist_ok=True)
        
        for i, img_path in enumerate(result_paths):
            try:
                img = Image.open(img_path)
                result_filename = f"{prefix}_result_{i+1:02d}.jpg"
                result_path = os.path.join(result_dir, result_filename)
                img.save(result_path)
            except Exception as e:
                print(f"保存结果图像 {img_path} 失败: {str(e)}")
        
        print(f"所有结果图像已保存到: {result_dir}")
        
        return {
            "grid_path": grid_path,
            "query_path": query_path if query_image_path else None,
            "results_dir": result_dir
        }
    
    def visualize_and_save_search_results(self, query_text=None, query_image_path=None, 
                                       results=None, grid_size=(2, 2), image_size=(400, 400),
                                       save_dir=None, prefix=""):
        """
        可视化并保存检索结果
        
        参数:
        query_text: 查询文本
        query_image_path: 查询图像路径
        results: 搜索结果
        grid_size: 结果网格尺寸
        image_size: 图像显示尺寸
        save_dir: 保存目录
        prefix: 文件名前缀
        """
        # 如果没有提供结果，则执行搜索
        if results is None:
            if query_text:
                _, results = self.text_to_image_search(query_text, limit=grid_size[0]*grid_size[1])
                print(f"查询文本: {query_text}")
                print("results:",results)
            elif query_image_path:
                _, results = self.image_to_image_search(query_image_path, limit=grid_size[0]*grid_size[1])
                print(f"查询图片:")
                self.display_query_image(query_image_path)
                print("results:",results)
            else:
                raise ValueError("必须提供query_text或query_image_path")
        
        # 创建结果网格
        results_grid, result_paths = self.create_results_grid(
            results,
            images_per_row=grid_size[0],
            images_per_column=grid_size[1],
            image_size=image_size
        )
        
        # 显示结果网格
        display(results_grid)
        
        # 保存结果
        save_info = self.save_search_results(
            results_grid=results_grid,
            result_paths=result_paths,
            query_text=query_text,
            query_image_path=query_image_path,
            save_dir=save_dir,
            prefix=prefix
        )
        
        return save_info
if __name__ == "__main__":
    from multimodal_vector_db_manager import MilvusMultimodalManager
    from clip_encoder import ChineseClipEncoder
    # from multimodal_retrieval_system import MultimodalRetrievalSystem

    # 初始化组件
    clip_encoder = ChineseClipEncoder(model_name="ViT-B-16")
    milvus_manager = MilvusMultimodalManager(uri="http://localhost:19530",collection_name="multimodal_chinese_clip")
   

    # 初始化检索系统（指定结果保存目录）
    searcher = MultimodalRetrievalSystem(
        milvus_manager.milvus_client,
        milvus_manager.collection_name,
        clip_encoder,
        results_dir="search_results"  # 自定义保存目录
    )

    # 以文搜图并保存结果
    text_query = "小桥流水人家"
    save_info = searcher.visualize_and_save_search_results(
        query_text=text_query,
        grid_size=(2, 2),
        prefix="sunset_beach"  # 自定义文件名前缀
    )

    print(f"结果网格保存位置: {save_info['grid_path']}")
    print(f"结果图像目录: {save_info['results_dir']}")

    # 以图搜图并保存结果
    image_query = "./images_data/2007_007084.jpg"
    save_info = searcher.visualize_and_save_search_results(
        query_image_path=image_query,
        grid_size=(2, 2),
        prefix="city_night"  # 自定义文件名前缀
    )

    print(f"查询图像保存位置: {save_info['query_path']}")
    print(f"结果网格保存位置: {save_info['grid_path']}")