# 🔍 多模态图像检索系统

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![PyTorch](https://img.shields.io/badge/PyTorch-2.6.0-red.svg)
![Milvus](https://img.shields.io/badge/Milvus-2.5.10-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**基于中文CLIP模型和Milvus向量数据库的智能多模态图像检索系统**

支持文本搜图 📝➡️🖼️ | 以图搜图 🖼️➡️🖼️ | 图像分类 🏷️ | 实时搜索 ⚡

</div>

## 📋 目录

- [✨ 功能特性](#-功能特性)
- [🏗️ 系统架构](#️-系统架构)
- [🚀 快速开始](#-快速开始)
- [📦 环境安装](#-环境安装)
- [🎯 使用指南](#-使用指南)
- [📁 项目结构](#-项目结构)
- [🔧 API文档](#-api文档)
- [🎨 界面展示](#-界面展示)
- [⚡ 性能优化](#-性能优化)
- [❓ 常见问题](#-常见问题)
- [🤝 贡献指南](#-贡献指南)

## ✨ 功能特性

### 🎯 核心功能
- **🔍 多模态检索**: 支持文本到图像和图像到图像的跨模态检索
- **🧠 中文优化**: 基于Chinese-CLIP模型，专为中文语境优化
- **⚡ 高效搜索**: 使用Milvus向量数据库实现毫秒级相似性搜索
- **🏷️ 智能分类**: 自动图像分类和标签生成功能
- **📊 可视化展示**: 直观的搜索结果网格展示和统计信息

### 🌐 多样化界面
- **💎 Streamlit界面**: 现代化、响应式的Web应用界面
- **🎨 Gradio界面**: 简洁易用的交互式界面
- **🔍 Google风格**: 仿Google搜索的经典界面设计
- **📱 静态HTML**: 轻量级的纯前端界面

### 🔧 技术特性
- **🏗️ 模块化架构**: 松耦合设计，易于扩展和维护
- **🚀 RESTful API**: 完整的API接口，支持第三方集成
- **📈 批量处理**: 支持大规模图像数据的批量导入和处理
- **💾 持久化存储**: 完整的数据持久化和备份方案

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        A[Streamlit Web UI]
        B[Gradio UI]
        C[HTML静态页面]
    end
    
    subgraph "API服务层"
        D[FastAPI服务器]
        E[文本搜索API]
        F[图像搜索API]
        G[健康检查API]
    end
    
    subgraph "核心业务层"
        H[MultimodalRetrievalSystem<br/>多模态检索系统]
        I[ChineseClipEncoder<br/>中文CLIP编码器]
        J[MilvusMultimodalManager<br/>向量数据库管理器]
    end
    
    subgraph "数据处理层"
        K[图像预处理]
        L[文本预处理]
        M[向量编码]
        N[相似性搜索]
    end
    
    subgraph "存储层"
        O[(Milvus向量数据库)]
        P[(MinIO对象存储)]
        Q[(etcd元数据存储)]
        R[本地文件系统]
    end
    
    subgraph "模型层"
        S[Chinese-CLIP ViT-B-16]
        T[图像编码器]
        U[文本编码器]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    D --> F
    D --> G
    
    E --> H
    F --> H
    
    H --> I
    H --> J
    
    I --> K
    I --> L
    I --> M
    
    M --> N
    N --> O
    
    J --> O
    J --> P
    J --> Q
    
    H --> R
    
    I --> S
    S --> T
    S --> U
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#e8f5e8
    style O fill:#fff3e0
    style S fill:#fce4ec
```

### 数据流程图

```mermaid
graph LR
    subgraph "数据输入"
        A[用户文本查询]
        B[用户上传图片]
    end
    
    subgraph "预处理阶段"
        C[文本分词与清理]
        D[图像格式转换与缩放]
    end
    
    subgraph "编码阶段"
        E[Chinese-CLIP文本编码器]
        F[Chinese-CLIP图像编码器]
        G[512维向量嵌入]
    end
    
    subgraph "检索阶段"
        H[向量相似性搜索]
        I[余弦相似度计算]
        J[Top-K结果排序]
    end
    
    subgraph "后处理阶段"
        K[结果过滤与去重]
        L[图像路径解析]
        M[结果网格生成]
    end
    
    subgraph "输出展示"
        N[搜索结果图片]
        O[相似度分数]
        P[结果统计信息]
    end
    
    A --> C
    B --> D
    
    C --> E
    D --> F
    
    E --> G
    F --> G
    
    G --> H
    H --> I
    I --> J
    
    J --> K
    K --> L
    L --> M
    
    M --> N
    M --> O
    M --> P
    
    style A fill:#e3f2fd
    style B fill:#e3f2fd
    style G fill:#f3e5f5
    style H fill:#e8f5e8
    style N fill:#fff3e0
```

## 📦 环境安装

### 方式一：使用Conda（推荐）

```bash
# 创建conda环境
conda create -n multimodal-search python=3.10
conda activate multimodal-search

# 安装PyTorch（CUDA版本）
pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124
# CUDA 12.6

# 安装其他依赖
pip install cn_clip pymilvus fastapi uvicorn streamlit gradio pillow tqdm python-multipart requests
```

### 方式二：使用uv（现代Python包管理器）

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建项目环境
uv venv multimodal-search
source multimodal-search/bin/activate  # Linux/Mac
# 或 multimodal-search\Scripts\activate  # Windows

# 安装依赖
uv pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124
uv pip install cn_clip pymilvus fastapi uvicorn streamlit gradio pillow tqdm python-multipart requests
```

### 方式三：使用pip

```bash
# 创建虚拟环境
python -m venv multimodal-search
source multimodal-search/bin/activate  # Linux/Mac
# 或 multimodal-search\Scripts\activate  # Windows

# 安装PyTorch
pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124

# 安装其他依赖
pip install cn_clip pymilvus fastapi uvicorn streamlit gradio pillow tqdm python-multipart requests
```

### 启动Milvus向量数据库

```bash
# 下载并启动Milvus
wget https://github.com/milvus-io/milvus/releases/download/v2.5.10/milvus-standalone-docker-compose.yml -O docker-compose.yml
docker compose up -d

# 验证服务状态
docker ps
```

### 环境验证

```bash
# 检查Python环境
python --version  # 应该是3.8+

# 检查PyTorch和CUDA
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

# 检查Milvus连接
python -c "from pymilvus import connections; connections.connect('default', host='localhost', port='19530'); print('Milvus连接成功')"

# 检查中文CLIP模型
python -c "import cn_clip.clip as clip; print('Chinese-CLIP可用模型:', clip.available_models())"
```

## 🎯 使用指南 🚀 快速开始

### 1. 数据准备和导入

```python
from clip_encoder import ChineseClipEncoder
from multimodal_vector_db_manager import MilvusMultimodalManager, ImageEmbeddingInserter

# 初始化组件
clip_encoder = ChineseClipEncoder(model_name="ViT-B-16")
milvus_manager = MilvusMultimodalManager(
    uri="http://localhost:19530",
    collection_name="multimodal_chinese_clip"
)

# 设置数据库集合
if milvus_manager.setup_collection(recreate=True):
    print("✅ 数据库集合设置成功")

# 批量导入图像数据
inserter = ImageEmbeddingInserter(milvus_manager, clip_encoder)
stats = inserter.process_images_and_insert(
    input_dir_path="your_image_dataset",  # 替换为你的图片目录
    ext_list=['*.jpg', '*.jpeg', '*.png'],
    batch_size=500
)
print(f"📊 导入完成！总记录数: {stats.get('row_count', 'N/A')}")
```

### 2. 启动服务

```bash
# 启动API后端服务
python retrieval_api.py
# 服务将在 http://localhost:8900 启动
```
### 💡 推荐使用方式

1. **首次使用**：使用修复版Streamlit界面获得最佳体验
   ```bash
   streamlit run streamlit_search_ui_fixed.py
   ```

2. **便捷启动**：使用启动脚本自动配置环境
   ```bash
   python run_streamlit.py
   ```

3. **API开发**：直接调用API接口进行集成
   ```bash
   curl -X POST "http://localhost:8900/text_search" -d "query_text=夕阳海滩"
   ```

### 3. 使用Web界面

#### Streamlit现代化界面（推荐）
- **修复版界面**：`streamlit run streamlit_search_ui_fixed.py`
  - ✅ 无弃用警告，全中文界面
  - 🔍 智能搜索历史记录
  - 🎨 现代化设计和交互
- **原版界面**：`streamlit run streamlit_search_ui.py`
  - ⚠️ 可能有弃用警告
- 访问 `http://localhost:8501`
- 支持文本搜索和图像上传搜索
- 实时搜索结果展示
- 响应式设计，支持移动端

#### 简化版Google风格界面
```bash
python simple_google_ui.py
# 访问显示的本地地址
```

### 4. 编程接口使用

```python
from multimodal_retrieval_system import MultimodalRetrievalSystem

# 初始化检索系统
searcher = MultimodalRetrievalSystem(
    milvus_manager.milvus_client,
    milvus_manager.collection_name,
    clip_encoder
)

# 文本搜索图像
query_text = "夕阳下的海滩"
_, results = searcher.text_to_image_search(query_text, limit=10)

# 图像搜索相似图像
query_image = "path/to/your/image.jpg"
_, results = searcher.image_to_image_search(query_image, limit=10)

# 可视化搜索结果
searcher.visualize_search_results(
    query_text=query_text,
    grid_size=(2, 5),  # 2行5列
    image_size=(300, 300)
)
```

## 📁 项目结构

```
search-milvus/
├── 📂 核心模块
│   ├── clip_encoder.py                    # 🧠 中文CLIP编码器
│   ├── multimodal_vector_db_manager.py    # 🗄️ 向量数据库管理
│   ├── multimodal_retrieval_system.py     # 🔍 多模态检索系统
│   └── retrieval_api.py                   # 🚀 FastAPI服务接口
├── 📂 用户界面
│   ├── streamlit_search_ui.py             # 💎 Streamlit界面（原版）
│   ├── streamlit_search_ui_fixed.py       # 💎 Streamlit界面（修复版，推荐）
│   ├── simple_google_ui.py                # 📱 简化版Google风格界面
│   └── run_streamlit.py                   # 🚀 Streamlit启动脚本
├── 📂 配置文件
│   ├── docker-compose.yml                 # 🐳 Milvus服务配置
│   ├── environment.yml                    # 🐍 Conda环境配置
│   └── requirements.txt                   # 📦 Python依赖列表
├── 📂 数据目录
│   ├── chinese_clip_model/                # 🤖 CLIP模型文件
│   │   └── clip_cn_vit-b-16.pt           # 🧠 预训练模型权重
│   ├── images_data/                       # 🖼️ 示例图片数据
│   ├── search_results/                    # 📊 搜索结果缓存
│   │   ├── image_queries/                 # 🖼️ 图像搜索结果
│   │   └── text_queries/                  # 📝 文本搜索结果
│   ├── api_search_results/                # 🔗 API搜索结果
│   │   ├── image_queries/                 # 🖼️ API图像搜索结果
│   │   └── text_queries/                  # 📝 API文本搜索结果
│   ├── api_temp_uploads/                  # 📤 临时上传文件
│   └── volumes/                           # 💾 Milvus数据存储
│       ├── minio/                         # 📁 向量数据存储
│       ├── etcd/                          # 🗃️ 元数据存储
│       └── milvus/                        # ⚙️ 服务配置数据
└── 📂 文档
    ├── readme.md                          # 📖 原版项目说明
    ├── NEW_README.md                      # 📋 新版详细文档（本文档）
    └── STREAMLIT_使用说明.md              # 🎨 Streamlit界面使用指南
```

### 项目结构图

```mermaid
graph TD
    A[search-milvus/] --> B[核心模块]
    A --> C[用户界面]
    A --> D[配置文件]
    A --> E[数据目录]
    A --> F[文档]

    B --> B1[clip_encoder.py<br/>中文CLIP编码器]
    B --> B2[multimodal_vector_db_manager.py<br/>向量数据库管理]
    B --> B3[multimodal_retrieval_system.py<br/>多模态检索系统]
    B --> B4[retrieval_api.py<br/>FastAPI服务接口]

    C --> C1[streamlit_search_ui_fixed.py<br/>Streamlit修复版界面 ⭐推荐]
    C --> C2[streamlit_search_ui.py<br/>Streamlit原版界面]
    C --> C3[simple_google_ui.py<br/>简化版Google风格界面]
    C --> C4[run_streamlit.py<br/>Streamlit启动脚本]

    D --> D1[docker-compose.yml<br/>Milvus服务配置]
    D --> D2[requirements.txt<br/>Python依赖]
    D --> D3[environment.yml<br/>Conda环境配置]

    E --> E1[chinese_clip_model/<br/>CLIP模型文件]
    E --> E2[images_data/<br/>示例图片]
    E --> E3[search_results/<br/>搜索结果缓存]
    E --> E4[api_search_results/<br/>API搜索结果]
    E --> E5[api_temp_uploads/<br/>临时上传文件]
    E --> E6[volumes/<br/>Milvus数据存储]

    F --> F1[NEW_README.md<br/>新版详细文档]
    F --> F2[readme.md<br/>原版项目说明]
    F --> F3[STREAMLIT_使用说明.md<br/>界面使用指南]

    style B fill:#e8f5e8
    style C fill:#e1f5fe
    style C1 fill:#4caf50,color:#fff
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style F fill:#fce4ec
```

### 核心模块说明

#### 🧠 clip_encoder.py
中文CLIP模型封装，提供统一的编码接口：
- **文本编码**: 支持单个和批量文本向量化
- **图像编码**: 支持多种图像格式的向量化
- **图像分类**: 基于CLIP的零样本图像分类
- **模型管理**: 自动下载和缓存模型文件

#### 🗄️ multimodal_vector_db_manager.py
Milvus向量数据库的完整管理方案：
- **集合管理**: 创建、删除、索引、加载集合
- **数据插入**: 批量图像数据处理和向量插入
- **索引优化**: 自动选择最优索引参数
- **错误处理**: 完善的异常处理和重试机制

#### 🔍 multimodal_retrieval_system.py
多模态检索的核心实现：
- **跨模态搜索**: 文本-图像、图像-图像检索
- **结果处理**: 相似度排序和结果过滤
- **可视化**: 搜索结果的网格展示
- **结果保存**: 自动保存搜索结果和统计信息

#### 🚀 retrieval_api.py
RESTful API服务接口：
- **文本搜索API**: `/text_search` 端点
- **图像搜索API**: `/image_search` 端点
- **健康检查**: `/health` 端点
- **文件处理**: 支持多种图像格式上传

### 用户界面说明

#### 💎 streamlit_search_ui_fixed.py（推荐）
修复版Streamlit界面，解决了所有已知问题：
- ✅ **无弃用警告**: 修复了 `use_column_width` 等弃用参数
- 🇨🇳 **全中文界面**: 完全中文化的用户界面
- 🔍 **搜索历史**: 智能保存和快速重复搜索
- 🎨 **现代化设计**: 优化的视觉效果和交互体验

#### 📝 streamlit_search_ui.py
原版Streamlit界面：
- ⚠️ **可能有警告**: 使用了一些弃用的参数
- 🌐 **基础功能**: 完整的搜索功能
- 📱 **响应式设计**: 支持移动端访问

#### 🚀 run_streamlit.py
Streamlit启动脚本：
- 🔧 **自动配置**: 自动设置环境变量和配置
- 📋 **依赖检查**: 检查必要的依赖包
- 🚀 **一键启动**: 简化启动流程

## 🔧 API文档

### 基础信息
- **服务地址**: `http://localhost:8900`
- **API版本**: v1.0
- **数据格式**: JSON
- **文件上传**: 支持 multipart/form-data

### 端点详情

#### 1. 文本搜索 API

```http
POST /text_search
```

**参数**:
- `query_text` (string, required): 搜索文本
- `limit` (int, optional): 返回结果数量，默认20
- `grid_size` (string, optional): 结果网格尺寸，默认"5x4"

**请求示例**:
```bash
curl -X POST "http://localhost:8900/text_search" \
     -d "query_text=夕阳海滩&limit=10&grid_size=2x5"
```

**响应示例**:
```json
{
  "status": "success",
  "query_text": "夕阳海滩",
  "result_count": 10,
  "search_time": 0.15,
  "results_grid": "path/to/results_grid.jpg",
  "individual_results_dir": "path/to/individual/results/"
}
```

#### 2. 图像搜索 API

```http
POST /image_search
```

**参数**:
- `file` (file, required): 上传的图像文件
- `limit` (int, optional): 返回结果数量，默认20
- `grid_size` (string, optional): 结果网格尺寸，默认"5x4"

**请求示例**:
```bash
curl -X POST "http://localhost:8900/image_search" \
     -F "file=@/path/to/image.jpg" \
     -F "limit=10" \
     -F "grid_size=2x5"
```

**响应示例**:
```json
{
  "status": "success",
  "query_image": "image.jpg",
  "result_count": 10,
  "search_time": 0.23,
  "results_grid": "path/to/results_grid.jpg",
  "individual_results_dir": "path/to/individual/results/"
}
```

#### 3. 健康检查 API

```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0"
}
```

## 🎨 界面展示

### 💎 Streamlit现代化界面

#### 🌟 修复版界面（推荐）
- **🎯 特点**: 无警告、全中文、现代化设计
- **🚀 启动**: `streamlit run streamlit_search_ui_fixed.py` 或 `python run_streamlit.py`
- **🌐 访问**: `http://localhost:8501`
- **📱 功能**:
  - ✅ 无弃用警告，全中文界面
  - 🔍 智能搜索历史记录
  - 📊 详细搜索统计和结果展示
  - 🎨 现代化设计和交互效果
  - 📱 响应式布局，移动端适配
  - 💡 智能搜索建议和错误提示

#### 📝 原版界面
- **🎯 特点**: 基础功能完整
- **🚀 启动**: `streamlit run streamlit_search_ui.py`
- **⚠️ 注意**: 可能显示弃用警告信息

### 🔍 简化版Google风格界面
- **🎯 特点**: 经典设计、轻量级、易于使用
- **🚀 启动**: `python simple_google_ui.py`
- **📱 功能**:
  - Google搜索风格设计
  - 简洁的搜索界面
  - 基础的结果展示

## ⚡ 性能优化

### 🚀 系统性能指标
- **搜索延迟**: < 200ms (文本搜索)
- **搜索延迟**: < 500ms (图像搜索)
- **并发支持**: 100+ 并发请求
- **数据规模**: 支持百万级图像检索
- **内存占用**: < 4GB (包含模型)

### 🔧 优化策略

#### 1. 模型优化
```python
# GPU加速
clip_encoder = ChineseClipEncoder(
    model_name="ViT-B-16",
    device="cuda"  # 使用GPU加速
)

# 批量处理
texts = ["查询1", "查询2", "查询3"]
vectors = clip_encoder.encode_text(texts)  # 批量编码
```

#### 2. 数据库优化
```python
# 索引参数优化
index_params = {
    "index_type": "IVF_FLAT",
    "metric_type": "COSINE",
    "params": {"nlist": 1024}  # 根据数据量调整
}

# 批量插入优化
inserter.process_images_and_insert(
    input_dir_path="images/",
    batch_size=1000,  # 增大批次大小
    ext_list=['*.jpg', '*.png']
)
```

#### 3. 缓存策略
```python
# 结果缓存
@lru_cache(maxsize=1000)
def cached_text_search(query_text, limit):
    return searcher.text_to_image_search(query_text, limit)

# 向量缓存
vector_cache = {}
def get_cached_vector(text):
    if text not in vector_cache:
        vector_cache[text] = encoder.encode_text(text)
    return vector_cache[text]
```

#### 4. 部署优化
```bash
# 使用更多worker进程
uvicorn retrieval_api:app --host 0.0.0.0 --port 8900 --workers 4

# 使用Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker retrieval_api:app
```

### 📊 性能监控
```python
import time
import psutil

def monitor_performance():
    start_time = time.time()

    # 执行搜索
    results = searcher.text_to_image_search("测试查询", 10)

    # 计算性能指标
    search_time = time.time() - start_time
    memory_usage = psutil.Process().memory_info().rss / 1024 / 1024  # MB

    print(f"搜索耗时: {search_time:.3f}s")
    print(f"内存使用: {memory_usage:.1f}MB")
```

## ❓ 常见问题

### 🔧 安装和配置问题

#### Q1: Milvus服务启动失败
**症状**: `docker compose up -d` 后容器无法启动

**解决方案**:
```bash
# 检查端口占用
netstat -tulpn | grep :19530

# 清理旧容器和数据
docker compose down -v
docker system prune -f

# 重新启动
docker compose up -d

# 检查日志
docker compose logs milvus-standalone
```

#### Q2: 中文CLIP模型下载失败
**症状**: 模型下载超时或网络错误

**解决方案**:
```bash
# 方法1: 设置代理
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port

# 方法2: 手动下载模型
mkdir -p chinese_clip_model
# 从官方仓库下载模型文件到该目录

# 方法3: 使用国内镜像
pip install cn_clip -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### Q3: CUDA内存不足
**症状**: `RuntimeError: CUDA out of memory`

**解决方案**:
```python
# 减少批处理大小
inserter.process_images_and_insert(
    batch_size=100,  # 从500减少到100
    input_dir_path="images/"
)

# 使用CPU模式
clip_encoder = ChineseClipEncoder(
    model_name="ViT-B-16",
    device="cpu"
)

# 清理GPU缓存
import torch
torch.cuda.empty_cache()
```

### 🔍 搜索和结果问题

#### Q4: 搜索结果不相关
**可能原因和解决方案**:

1. **数据集问题**:
   ```python
   # 检查数据集覆盖度
   stats = milvus_manager.get_collection_stats()
   print(f"数据库中图片数量: {stats['row_count']}")

   # 确保数据集包含相关图片
   ```

2. **查询词优化**:
   ```python
   # 使用更具体的描述
   query_text = "夕阳下的海滩风景"  # 而不是 "海滩"

   # 尝试不同的表达方式
   queries = ["海边日落", "夕阳海滩", "黄昏海景"]
   ```

3. **模型限制**:
   ```python
   # 尝试不同的模型
   encoder = ChineseClipEncoder(model_name="ViT-L-14")  # 更大的模型
   ```

#### Q5: 搜索速度慢
**优化方案**:

1. **索引优化**:
   ```python
   # 调整索引参数
   index_params = {
       "index_type": "IVF_PQ",  # 使用PQ压缩
       "metric_type": "COSINE",
       "params": {"nlist": 2048, "m": 8}
   }
   ```

2. **硬件升级**:
   - 使用SSD存储
   - 增加内存容量
   - 使用GPU加速

3. **数据库调优**:
   ```python
   # 调整搜索参数
   search_params = {"nprobe": 64}  # 减少nprobe值
   results = milvus_client.search(
       collection_name=collection_name,
       data=[vector],
       anns_field="vectors",
       param=search_params,
       limit=limit
   )
   ```

### 🌐 部署和生产问题

#### Q6: 生产环境部署建议
**推荐配置**:

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  milvus:
    image: milvusdb/milvus:v2.5.10
    environment:
      - MILVUS_CONFIG_PATH=/milvus/configs/milvus.yaml
    volumes:
      - ./configs/milvus.yaml:/milvus/configs/milvus.yaml
      - milvus_data:/var/lib/milvus
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G

  api:
    build: .
    ports:
      - "8900:8900"
    environment:
      - MILVUS_HOST=milvus
      - WORKERS=4
    depends_on:
      - milvus
```

**性能调优**:
```bash
# 系统级优化
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
sysctl -p

# 使用专业WSGI服务器
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
         --bind 0.0.0.0:8900 \
         --timeout 300 \
         retrieval_api:app
```

## 🤝 贡献指南

### 🎯 贡献方式

我们欢迎各种形式的贡献！
https://github.com/jinwater88/search-image-milvus
#### 🐛 报告Bug
1. 在 [Issues](https://github.com/jinwater88/search-image-milvus/issues) 中搜索是否已存在相同问题
2. 创建新Issue，包含：
   - 详细的问题描述
   - 复现步骤
   - 系统环境信息
   - 错误日志

#### 💡 功能建议
1. 在 [Discussions](https://github.com/jinwater88/search-image-milvus/discussions) 中讨论新功能
2. 创建Feature Request Issue
3. 提供详细的需求描述和使用场景

#### 🔧 代码贡献
1. Fork项目到你的GitHub账户
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

### 📋 开发规范

#### 代码风格
```bash
# 安装开发依赖
pip install black flake8 isort pytest

# 代码格式化
black .
isort .

# 代码检查
flake8 .

# 运行测试
pytest tests/
```

#### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <description>

# 示例
feat(ui): add streamlit search interface
fix(api): resolve image upload timeout issue
docs(readme): update installation guide
```

#### 测试要求
```python
# 为新功能添加测试
def test_text_search():
    encoder = ChineseClipEncoder()
    searcher = MultimodalRetrievalSystem(...)

    results = searcher.text_to_image_search("测试查询", limit=5)
    assert len(results) <= 5
    assert all(isinstance(r, dict) for r in results)
```

### 🏆 贡献者

感谢所有为项目做出贡献的开发者！

<a href="https://github.com/jinwater88/search-image-milvus/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=jinwater88/search-image-milvus" />
</a>

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 LICENSE 文件了解详情。

## 🙏 致谢

### 开源项目
- [Chinese-CLIP](https://github.com/OFA-Sys/Chinese-CLIP) - 优秀的中文多模态预训练模型
- [Milvus](https://milvus.io/) - 高性能向量数据库
- [PyTorch](https://pytorch.org/) - 深度学习框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Streamlit](https://streamlit.io/) - 快速构建数据应用的框架
- [Gradio](https://gradio.app/) - 机器学习模型的快速UI构建工具

### 技术支持
- OpenAI CLIP论文和实现思路
- Hugging Face社区的模型和工具
- Docker和容器化技术栈

## 📞 联系我们

- **项目主页**: [GitHub Repository](https://github.com/jinwater88/search-image-milvus)
- **问题反馈**: [GitHub Issues](https://github.com/jinwater88/search-image-milvus/issues)
- **功能讨论**: [GitHub Discussions](https://github.com/jinwater88/search-image-milvus/discussions)
- **邮件联系**: <EMAIL>

---

<div align="center">

**🚀 开始构建您的多模态图像检索系统吧！**

如果这个项目对您有帮助，请给我们一个 ⭐ Star！

[⬆️ 回到顶部](#-多模态图像检索系统)

</div>
