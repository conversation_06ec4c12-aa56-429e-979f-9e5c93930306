# Streamlit多模态搜索UI界面
import streamlit as st
import requests
import os
import base64
from io import BytesIO
from PIL import Image
import json
import time
from typing import List, Tuple, Optional

# 配置页面
st.set_page_config(
    page_title="多模态图像搜索系统",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 设置中文环境
import locale
import warnings
import os

# 忽略Streamlit的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*use_column_width.*")

# 设置中文语言环境
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese')
    except:
        pass  # 如果设置失败，使用默认语言

# FastAPI服务地址
FASTAPI_URL = "http://localhost:8900"

# 自定义CSS样式
def load_css():
    st.markdown("""
    <style>
    /* 全局样式 */
    .main {
        padding-top: 2rem;
    }
    
    /* 标题样式 */
    .main-title {
        text-align: center;
        color: #1f77b4;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        background: linear-gradient(90deg, #1f77b4, #ff7f0e, #2ca02c, #d62728);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
    
    /* 搜索框样式 */
    .search-container {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    /* 按钮样式 */
    .stButton > button {
        width: 100%;
        border-radius: 10px;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* 结果展示样式 */
    .result-container {
        background: white;
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    
    .result-image {
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;
    }
    
    .result-image:hover {
        transform: scale(1.05);
    }
    
    /* 统计信息样式 */
    .stats-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    /* 侧边栏样式 */
    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    /* 加载动画 */
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #1f77b4;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* 错误提示样式 */
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #dc3545;
        margin: 1rem 0;
    }
    
    /* 成功提示样式 */
    .success-message {
        background: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

# 搜索功能函数
def search_by_text(query_text: str, limit: int = 20) -> List[Tuple[str, str]]:
    """通过文本搜索调用FastAPI接口"""
    try:
        response = requests.post(
            f"{FASTAPI_URL}/text_search",
            params={"query_text": query_text, "limit": limit, "grid_size": "5x4"},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            return process_results(data)
        else:
            st.error(f"搜索失败: {response.json().get('detail', '未知错误')}")
            return []
    except requests.exceptions.RequestException as e:
        st.error(f"网络连接错误: {str(e)}")
        return []
    except Exception as e:
        st.error(f"搜索异常: {str(e)}")
        return []

def search_by_image(image_file) -> List[Tuple[str, str]]:
    """通过图像搜索调用FastAPI接口"""
    try:
        files = {"file": ("image.jpg", image_file, "image/jpeg")}
        response = requests.post(
            f"{FASTAPI_URL}/image_search",
            files=files,
            params={"limit": 20, "grid_size": "5x4"},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            return process_results(data)
        else:
            st.error(f"图像搜索失败: {response.json().get('detail', '未知错误')}")
            return []
    except requests.exceptions.RequestException as e:
        st.error(f"网络连接错误: {str(e)}")
        return []
    except Exception as e:
        st.error(f"图像搜索异常: {str(e)}")
        return []

def process_results(data: dict) -> List[Tuple[str, str]]:
    """处理搜索结果，加载图片和文件名"""
    if "individual_results_dir" not in data:
        return []
        
    result_dir = data["individual_results_dir"]
    if not os.path.exists(result_dir):
        return []
        
    image_files = [f for f in os.listdir(result_dir) if f.endswith((".jpg", ".png", ".jpeg"))]
    
    results = []
    for img_file in sorted(image_files)[:20]:
        img_path = os.path.join(result_dir, img_file)
        try:
            results.append((img_path, os.path.basename(img_path)))
        except Exception as e:
            st.warning(f"加载图片失败: {img_path} | 错误: {str(e)}")
            continue
            
    return results

def check_api_status() -> bool:
    """检查API服务状态"""
    try:
        response = requests.get(f"{FASTAPI_URL}/", timeout=5)
        return response.status_code == 200
    except Exception as e:
        st.error(f"API服务连接失败: {str(e)}")
        return False

def display_image_grid(results: List[Tuple[str, str]], columns: int = 4):
    """以网格形式显示搜索结果"""
    if not results:
        st.info("🔍 没有找到相关图片，请尝试其他搜索词或上传不同的图片")
        return

    # 计算行数
    rows = (len(results) + columns - 1) // columns

    for row in range(rows):
        cols = st.columns(columns)
        for col_idx in range(columns):
            result_idx = row * columns + col_idx
            if result_idx < len(results):
                img_path, img_name = results[result_idx]
                with cols[col_idx]:
                    try:
                        image = Image.open(img_path)
                        st.image(
                            image,
                            caption=f"📷 {img_name}",
                            use_container_width=True,
                            output_format="JPEG"
                        )

                        # 添加图片信息
                        with st.expander("📊 图片信息", expanded=False):
                            st.write(f"**文件名**: {img_name}")
                            st.write(f"**尺寸**: {image.size[0]} × {image.size[1]}")
                            st.write(f"**格式**: {image.format}")

                    except Exception as e:
                        st.error(f"❌ 无法显示图片: {img_name}")
                        st.caption(f"错误详情: {str(e)}")

# 初始化session state
def init_session_state():
    """初始化会话状态"""
    if 'query_text' not in st.session_state:
        st.session_state.query_text = ""
    if 'search_history' not in st.session_state:
        st.session_state.search_history = []
    if 'last_results' not in st.session_state:
        st.session_state.last_results = []

# 主界面
def main():
    # 初始化会话状态
    init_session_state()

    # 加载CSS样式
    load_css()

    # 页面标题
    st.markdown('<h1 class="main-title">🔍 多模态图像搜索系统</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">基于中文CLIP模型的智能图像检索平台 | 支持文本搜图和以图搜图</p>', unsafe_allow_html=True)
    
    # 检查API状态
    api_status = check_api_status()
    if not api_status:
        st.error("⚠️ 后端API服务未启动，请先启动FastAPI服务 (python retrieval_api.py)")
        st.stop()
    
    # 侧边栏配置
    with st.sidebar:
        st.header("🛠️ 搜索配置")
        
        # 搜索模式选择
        search_mode = st.radio(
            "选择搜索模式",
            ["文本搜索", "图像搜索"],
            help="选择使用文本描述或上传图片进行搜索"
        )
        
        # 结果数量设置
        result_limit = st.slider(
            "结果数量",
            min_value=5,
            max_value=50,
            value=20,
            step=5,
            help="设置返回的搜索结果数量"
        )
        
        # 显示列数设置
        display_columns = st.slider(
            "显示列数",
            min_value=2,
            max_value=6,
            value=4,
            help="设置结果展示的列数"
        )
        
        st.divider()

        # 搜索历史
        if st.session_state.search_history:
            st.subheader("🕒 搜索历史")
            for i, history_query in enumerate(st.session_state.search_history[:5]):
                if st.button(f"🔍 {history_query}", key=f"history_{i}", use_container_width=True):
                    st.session_state.query_text = history_query
                    st.rerun()

            if st.button("🗑️ 清空历史", use_container_width=True):
                st.session_state.search_history = []
                st.rerun()

        st.divider()

        # 系统信息
        st.subheader("📊 系统状态")
        st.success("✅ API服务正常运行")
        st.info(f"🌐 服务地址: {FASTAPI_URL}")

        if st.session_state.last_results:
            st.metric("📸 上次搜索", f"{len(st.session_state.last_results)} 张图片")

        # 使用说明
        st.subheader("📖 使用指南")
        st.markdown("""
        **🔍 文本搜索：**
        - 输入中文描述词语
        - 例如：夕阳、海滩、城市夜景
        - 支持复合描述：夕阳下的海滩

        **🖼️ 图像搜索：**
        - 上传JPG/PNG格式图片
        - 系统会找到相似的图片
        - 支持拖拽上传

        **💡 搜索技巧：**
        - 使用具体的描述词效果更好
        - 可以描述颜色、场景、物体
        - 支持情感描述：温馨、浪漫等
        """)

    # 主要内容区域
    main_container = st.container()

    with main_container:
        if search_mode == "文本搜索":
            # 文本搜索界面
            st.subheader("📝 文本搜索")

            # 搜索输入框
            col1, col2 = st.columns([4, 1])
            with col1:
                query_text = st.text_input(
                    "请输入搜索描述",
                    value=st.session_state.query_text,
                    placeholder="例如：夕阳下的海滩、城市夜景、可爱的小猫...",
                    help="💡 输入中文描述，系统会找到相关的图片",
                    key="text_search_input"
                )

            with col2:
                st.write("")  # 占位符
                search_button = st.button("🔍 开始搜索", type="primary", use_container_width=True)

            # 预设查询示例
            st.subheader("💡 热门搜索")
            example_queries = ["夕阳海滩", "城市夜景", "可爱小猫", "山水风景", "现代建筑", "花朵特写"]

            # 使用更好的布局
            cols = st.columns(3)
            for i, example in enumerate(example_queries):
                with cols[i % 3]:
                    if st.button(f"🔍 {example}", key=f"example_{i}", use_container_width=True):
                        st.session_state.query_text = example
                        st.rerun()

            # 执行文本搜索
            if search_button and query_text:
                # 添加到搜索历史
                if query_text not in st.session_state.search_history:
                    st.session_state.search_history.insert(0, query_text)
                    st.session_state.search_history = st.session_state.search_history[:10]  # 保留最近10次

                with st.spinner("🔍 正在智能分析您的搜索需求，请稍候..."):
                    start_time = time.time()
                    results = search_by_text(query_text, result_limit)
                    search_time = time.time() - start_time

                if results:
                    st.session_state.last_results = results

                    # 显示搜索统计
                    st.success(f"🎉 搜索完成！为您找到 {len(results)} 张相关图片")

                    # 统计信息卡片
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("🔍 搜索词", query_text)
                    with col2:
                        st.metric("📸 找到图片", f"{len(results)} 张")
                    with col3:
                        st.metric("⚡ 搜索耗时", f"{search_time:.2f} 秒")
                    with col4:
                        st.metric("🎯 匹配度", "高")

                    # 显示搜索结果
                    st.subheader(f"🖼️ 为您找到的相关图片")
                    st.caption(f"根据搜索词 \"{query_text}\" 找到以下 {len(results)} 张最相关的图片")

                    display_image_grid(results, display_columns)

                    # 操作按钮
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        if st.button("🔄 重新搜索", use_container_width=True):
                            st.rerun()
                    with col2:
                        if st.button("📥 保存结果", use_container_width=True):
                            st.info("💾 结果保存功能开发中，敬请期待...")
                    with col3:
                        if st.button("📤 分享结果", use_container_width=True):
                            st.info("🔗 分享功能开发中，敬请期待...")

                else:
                    st.warning("😔 很抱歉，没有找到与您搜索词相关的图片")
                    st.info("💡 建议您：")
                    st.markdown("""
                    - 🔄 尝试使用更具体的描述词
                    - 🎯 使用不同的表达方式
                    - 📝 检查搜索词的拼写
                    - 🖼️ 或者尝试上传图片进行搜索
                    """)

        else:
            # 图像搜索界面
            st.subheader("🖼️ 图像搜索")

            # 图片上传
            uploaded_file = st.file_uploader(
                "上传图片进行搜索",
                type=['jpg', 'jpeg', 'png'],
                help="支持JPG、JPEG、PNG格式，建议图片大小不超过10MB"
            )
            
            if uploaded_file is not None:
                # 显示上传的图片
                
                col1, col2 = st.columns([1, 2])

                with col1:
                    st.subheader(f"📤 查询图片")
                    image = Image.open(uploaded_file)
                    st.image(image, caption="上传的图片", use_column_width=True)

                    # 图片信息
                    # st.info(f"📤 查询图片:", uploaded_file)
                    st.info(f"""
                    **文件名：** {uploaded_file}
                    **图片尺寸：** {image.size[0]} × {image.size[1]}
                    **文件大小：** {uploaded_file.size / 1024:.1f} KB
                    """)

                with col2:
                    st.subheader("🔍 搜索操作")

                    if st.button("🚀 开始搜索相似图片", type="primary", use_container_width=True):
                        with st.spinner("🔍 正在搜索相似图片..."):
                            start_time = time.time()

                            # 重置文件指针
                            uploaded_file.seek(0)
                            results = search_by_image(uploaded_file)
                            search_time = time.time() - start_time

                        if results:
                            # 显示搜索统计
                            st.markdown(f"""
                            <div class="stats-container">
                                <h3>📊 搜索结果统计</h3>
                                <p><strong>查询图片：</strong>{uploaded_file.name}</p>
                                <p><strong>找到相似图片：</strong>{len(results)} 张</p>
                                <p><strong>搜索耗时：</strong>{search_time:.2f} 秒</p>
                            </div>
                            """, unsafe_allow_html=True)

                            # 显示搜索结果
                            st.subheader(f"🎯 相似图片结果 ({len(results)} 张)")
                            display_image_grid(results, display_columns)

                        else:
                            st.warning("😔 没有找到相似图片，请尝试上传其他图片")

            else:
                # 显示上传提示
                st.info("👆 请上传一张图片开始搜索")

                # 示例图片展示
                st.subheader("📷 示例图片")
                st.markdown("您可以尝试上传类似以下类型的图片：")

                example_images = [
                    "🌅 风景照片", "🏙️ 城市建筑", "🐱 动物照片",
                    "🌸 花卉植物", "🍕 美食图片", "👥 人物照片"
                ]

                cols = st.columns(3)
                for i, example in enumerate(example_images):
                    with cols[i % 3]:
                        st.markdown(f"**{example}**")

    # 页面底部信息
    st.divider()

    # 技术信息
    with st.expander("🔧 技术架构信息"):
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            **🧠 AI模型**
            - 中文CLIP模型
            - ViT-B-16架构
            - 多模态理解
            """)

        with col2:
            st.markdown("""
            **🗄️ 数据库**
            - Milvus向量数据库
            - 高效相似性搜索
            - 分布式存储
            """)

        with col3:
            st.markdown("""
            **🌐 Web框架**
            - Streamlit前端
            - FastAPI后端
            - RESTful API
            """)

    # 版权信息
    st.markdown("""
    <div style="text-align: center; color: #666; margin-top: 2rem;">
        <p>🚀 多模态图像搜索系统 | 基于中文CLIP + Milvus构建</p>
        <p>💡 支持文本搜图和以图搜图功能</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
